1.1:
Create a complete B2B official website for VPL brand's foreign trade network line and VPN services. The website should include:

**Brand & Services:**
- Brand name: "VPL" 
- Services: Foreign trade network lines, cross-border e-commerce external network lines, VPN services
- Key features to highlight: Encryption, Fast speed, Security, Stability, AES encryption, Tunnel encryption, RSA asymmetric encryption protocol, TLS encryption, SS (Shadowsocks)

**Technical Requirements:**
- Use React with Next.js framework for modern web development
- Implement responsive design for mobile, tablet, and desktop compatibility
- Include a comprehensive B2B contact/consultation form with multiple communication options (WeChat, QQ, phone, email, SMS)
- Add alphanumeric verification code system for form submissions
- Create an admin dashboard backend for managing inquiries and form submissions
- Implement real-time notifications for new form submissions
- Include email integration with configurable SMTP settings
- Add secure authentication system for admin access

**Content Structure:**
- Homepage showcasing VPL's VPN and network line services
- Service pages detailing encryption technologies (AES, RSA, TLS, tunnel encryption)
- Features highlighting security, speed, and stability
- B2B contact/consultation section
- Admin login area for backend management

**Design Requirements:**
- Professional B2B-focused design suitable for foreign trade clients
- Emphasize security and reliability in the visual design
- Include sections for technical specifications and service benefits
- Ensure cross-platform compatibility and responsive layout

Initialize the project using Next.js CLI and implement a complete solution with both frontend user experience and backend management capabilities.
















Add internationalization (i18n) support to the VPL B2B website project to support multiple languages including Chinese (Simplified and Traditional) and Russian, in addition to the existing English content. This should include:

1. **Language Selection**: Implement a language switcher component in the header that allows users to toggle between English, Chinese (简体中文), Chinese (繁體中文), and Russian (Русский)

2. **Content Translation**: Translate all website content including:
   - Navigation menus and buttons
   - Homepage hero sections, service descriptions, and feature explanations
   - Services page content (foreign trade networks, cross-border e-commerce, VPN services)
   - Features page encryption technology descriptions (AES, RSA, TLS, tunnel encryption, Shadowsocks)
   - Contact form labels, placeholders, and validation messages
   - Admin dashboard interface and messages
   - Email templates for notifications

3. **Technical Implementation**: 
   - Use Next.js internationalization features or a library like next-i18next
   - Create translation files for each language (en.json, zh-CN.json, zh-TW.json, ru.json)
   - Implement URL-based language routing (e.g., /en/, /zh/, /ru/)
   - Ensure proper RTL/LTR text direction support
   - Maintain SEO optimization for each language version

4. **Cultural Localization**:
   - Adapt contact methods and communication preferences for Chinese and Russian markets
   - Localize phone numbers, addresses, and business hours for different regions
   - Ensure currency and date/time formats are appropriate for each locale

5. **Admin Dashboard**: Add language management capabilities to the admin panel for content updates and translation management

The goal is to make the VPL website accessible to Chinese and Russian-speaking business clients in the foreign trade and VPN services market.
