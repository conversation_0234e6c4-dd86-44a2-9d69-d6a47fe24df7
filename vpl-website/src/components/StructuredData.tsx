interface StructuredDataProps {
  type: 'organization' | 'website' | 'service' | 'contactPage';
  locale: string;
  data?: any;
}

export default function StructuredData({ type, locale, data }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com';

  const getStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'VPL Network Solutions',
          alternateName: 'VPL',
          url: baseUrl,
          logo: `${baseUrl}/logo.png`,
          description: locale === 'zh-CN' 
            ? 'VPL提供专业的外贸网络线路、跨境电商网络解决方案和企业VPN服务，采用先进的加密技术。'
            : locale === 'zh-TW'
            ? 'VPL提供專業的外貿網絡線路、跨境電商網絡解決方案和企業VPN服務，採用先進的加密技術。'
            : locale === 'ru'
            ? 'VPL предоставляет профессиональные сетевые решения для внешней торговли, трансграничной электронной коммерции и корпоративные VPN-услуги с передовыми технологиями шифрования.'
            : 'VPL provides professional network solutions for foreign trade, cross-border e-commerce, and enterprise VPN services with advanced encryption technologies.',
          contactPoint: [
            {
              '@type': 'ContactPoint',
              telephone: '+86-400-VPL-NET',
              contactType: 'customer service',
              availableLanguage: ['English', 'Chinese', 'Russian'],
              areaServed: 'Worldwide',
            },
          ],
          address: {
            '@type': 'PostalAddress',
            addressCountry: 'CN',
            addressLocality: 'Shanghai',
            addressRegion: 'Shanghai',
          },
          sameAs: [
            // Add social media URLs when available
          ],
          foundingDate: '2020',
          numberOfEmployees: '50-100',
          industry: 'Telecommunications',
          serviceArea: {
            '@type': 'GeoCircle',
            geoMidpoint: {
              '@type': 'GeoCoordinates',
              latitude: 31.2304,
              longitude: 121.4737,
            },
            geoRadius: '20000000', // Global coverage
          },
        };

      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'VPL Network Solutions',
          url: baseUrl,
          description: locale === 'zh-CN' 
            ? 'VPL专业网络解决方案官方网站'
            : locale === 'zh-TW'
            ? 'VPL專業網絡解決方案官方網站'
            : locale === 'ru'
            ? 'Официальный сайт VPL - профессиональные сетевые решения'
            : 'VPL Network Solutions Official Website',
          inLanguage: locale,
          potentialAction: {
            '@type': 'SearchAction',
            target: `${baseUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string',
          },
          publisher: {
            '@type': 'Organization',
            name: 'VPL Network Solutions',
            logo: `${baseUrl}/logo.png`,
          },
        };

      case 'service':
        return {
          '@context': 'https://schema.org',
          '@type': 'Service',
          name: data?.name || 'VPN and Network Services',
          description: data?.description || 'Professional VPN and network solutions for businesses',
          provider: {
            '@type': 'Organization',
            name: 'VPL Network Solutions',
            url: baseUrl,
          },
          serviceType: 'Network Services',
          category: 'Telecommunications',
          areaServed: 'Worldwide',
          hasOfferCatalog: {
            '@type': 'OfferCatalog',
            name: 'VPL Services',
            itemListElement: [
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Foreign Trade Network Lines',
                  description: 'Dedicated network lines for international trade',
                },
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Cross-border E-commerce Solutions',
                  description: 'Network solutions for e-commerce platforms',
                },
              },
              {
                '@type': 'Offer',
                itemOffered: {
                  '@type': 'Service',
                  name: 'Enterprise VPN Services',
                  description: 'Secure VPN solutions for enterprises',
                },
              },
            ],
          },
        };

      case 'contactPage':
        return {
          '@context': 'https://schema.org',
          '@type': 'ContactPage',
          name: locale === 'zh-CN' 
            ? '联系VPL'
            : locale === 'zh-TW'
            ? '聯繫VPL'
            : locale === 'ru'
            ? 'Связаться с VPL'
            : 'Contact VPL',
          description: locale === 'zh-CN' 
            ? '联系VPL获取专业网络解决方案和VPN服务'
            : locale === 'zh-TW'
            ? '聯繫VPL獲取專業網絡解決方案和VPN服務'
            : locale === 'ru'
            ? 'Свяжитесь с VPL для получения профессиональных сетевых решений и VPN-услуг'
            : 'Contact VPL for professional network solutions and VPN services',
          url: locale === 'en' ? `${baseUrl}/contact` : `${baseUrl}/${locale}/contact`,
          mainEntity: {
            '@type': 'Organization',
            name: 'VPL Network Solutions',
            contactPoint: [
              {
                '@type': 'ContactPoint',
                telephone: '+86-400-VPL-NET',
                contactType: 'customer service',
                availableLanguage: ['English', 'Chinese', 'Russian'],
              },
              {
                '@type': 'ContactPoint',
                email: '<EMAIL>',
                contactType: 'sales',
                availableLanguage: ['English', 'Chinese', 'Russian'],
              },
            ],
          },
        };

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) {
    return null;
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
