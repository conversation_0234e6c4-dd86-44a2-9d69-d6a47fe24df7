'use client';

import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';

export default function Footer() {
  const t = useTranslations('footer');
  const locale = useLocale();

  const navigation = {
    services: [
      { name: t('links.foreignTrade'), href: locale === 'en' ? '/services#foreign-trade' : `/${locale}/services#foreign-trade` },
      { name: t('links.ecommerce'), href: locale === 'en' ? '/services#ecommerce' : `/${locale}/services#ecommerce` },
      { name: t('links.vpn'), href: locale === 'en' ? '/services#vpn' : `/${locale}/services#vpn` },
      { name: t('links.enterprise'), href: locale === 'en' ? '/services#enterprise' : `/${locale}/services#enterprise` },
    ],
    features: [
      { name: t('links.aes'), href: locale === 'en' ? '/features#aes' : `/${locale}/features#aes` },
      { name: t('links.rsa'), href: locale === 'en' ? '/features#rsa' : `/${locale}/features#rsa` },
      { name: t('links.tls'), href: locale === 'en' ? '/features#tls' : `/${locale}/features#tls` },
      { name: t('links.tunnel'), href: locale === 'en' ? '/features#tunnel' : `/${locale}/features#tunnel` },
    ],
    company: [
      { name: t('links.about'), href: locale === 'en' ? '/about' : `/${locale}/about` },
      { name: t('links.contact'), href: locale === 'en' ? '/contact' : `/${locale}/contact` },
      { name: t('links.support'), href: locale === 'en' ? '/support' : `/${locale}/support` },
      { name: t('links.privacy'), href: locale === 'en' ? '/privacy' : `/${locale}/privacy` },
    ],
    contact: [
      { name: t('links.wechatSupport'), href: locale === 'en' ? '/contact#wechat' : `/${locale}/contact#wechat` },
      { name: t('links.qqSupport'), href: locale === 'en' ? '/contact#qq' : `/${locale}/contact#qq` },
      { name: t('links.emailSupport'), href: locale === 'en' ? '/contact#email' : `/${locale}/contact#email` },
      { name: t('links.phoneSupport'), href: locale === 'en' ? '/contact#phone' : `/${locale}/contact#phone` },
    ],
  };
  return (
    <footer className="bg-gray-900" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">V</span>
              </div>
              <span className="text-2xl font-bold text-white">VPL</span>
            </div>
            <p className="text-sm leading-6 text-gray-300">
              {t('description')}
            </p>
            <div className="flex space-x-6">
              {/* Social media icons can be added here */}
            </div>
          </div>
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">{t('sections.services')}</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.services.map((item) => (
                    <li key={item.name}>
                      <Link href={item.href} className="text-sm leading-6 text-gray-300 hover:text-white">
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">{t('sections.features')}</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.features.map((item) => (
                    <li key={item.name}>
                      <Link href={item.href} className="text-sm leading-6 text-gray-300 hover:text-white">
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">{t('sections.company')}</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.company.map((item) => (
                    <li key={item.name}>
                      <Link href={item.href} className="text-sm leading-6 text-gray-300 hover:text-white">
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">{t('sections.contact')}</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.contact.map((item) => (
                    <li key={item.name}>
                      <Link href={item.href} className="text-sm leading-6 text-gray-300 hover:text-white">
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 lg:mt-24">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="text-xs leading-5 text-gray-400">
              {t('copyright')}
            </p>
            <div className="mt-4 sm:mt-0 flex space-x-6 text-xs text-gray-400">
              <Link href={locale === 'en' ? '/privacy' : `/${locale}/privacy`} className="hover:text-gray-300">{t('links.privacy')}</Link>
              <Link href={locale === 'en' ? '/terms' : `/${locale}/terms`} className="hover:text-gray-300">{t('links.terms')}</Link>
              <Link href={locale === 'en' ? '/security' : `/${locale}/security`} className="hover:text-gray-300">{t('links.security')}</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
