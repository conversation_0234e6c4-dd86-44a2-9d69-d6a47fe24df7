'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { locales, type Locale } from '@/i18n';

const localeNames = {
  en: 'English',
  zh: '简体中文',
  ru: 'Русский',
};

const localeFlags = {
  en: '🇺🇸',
  zh: '🇨🇳',
  ru: '🇷🇺',
};

export default function LanguageSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale() as Locale;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (locale: Locale) => {
    setIsOpen(false);

    // Remove the current locale from the pathname if it exists
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '') || '/';

    // Navigate to the new locale (always include locale prefix)
    const newPath = `/${locale}${pathWithoutLocale}`;
    router.push(newPath);
  };

  const getCurrentLanguageInfo = () => {
    return {
      flag: localeFlags[currentLocale],
      name: localeNames[currentLocale],
      code: currentLocale,
    };
  };

  const currentLang = getCurrentLanguageInfo();

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Language Switcher Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label="Select language"
      >
        <span className="text-lg" role="img" aria-label={`${currentLang.name} flag`}>
          {currentLang.flag}
        </span>
        <span className="hidden sm:block">{currentLang.name}</span>
        <span className="sm:hidden">{currentLang.code.toUpperCase()}</span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((locale) => {
              const isActive = locale === currentLocale;
              return (
                <button
                  key={locale}
                  onClick={() => handleLanguageChange(locale)}
                  className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 transition-colors ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                  role="menuitem"
                  disabled={isActive}
                >
                  <span className="text-lg" role="img" aria-label={`${localeNames[locale]} flag`}>
                    {localeFlags[locale]}
                  </span>
                  <div className="flex flex-col">
                    <span className="font-medium">{localeNames[locale]}</span>
                    <span className="text-xs text-gray-500 uppercase">{locale}</span>
                  </div>
                  {isActive && (
                    <svg className="w-4 h-4 ml-auto text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

// Mobile Language Switcher for smaller screens
export function MobileLanguageSwitcher() {
  const currentLocale = useLocale() as Locale;
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (locale: Locale) => {
    // Remove the current locale from the pathname if it exists
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '') || '/';

    // Navigate to the new locale (always include locale prefix)
    const newPath = `/${locale}${pathWithoutLocale}`;
    router.push(newPath);
  };

  return (
    <div className="px-3 py-2 border-t border-gray-200">
      <div className="text-sm font-medium text-gray-500 mb-2">Language / 语言 / Язык</div>
      <div className="grid grid-cols-2 gap-2">
        {locales.map((locale) => {
          const isActive = locale === currentLocale;
          return (
            <button
              key={locale}
              onClick={() => handleLanguageChange(locale)}
              className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors ${
                isActive
                  ? 'bg-blue-100 text-blue-700 font-medium'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
              disabled={isActive}
            >
              <span className="text-base" role="img" aria-label={`${localeNames[locale]} flag`}>
                {localeFlags[locale]}
              </span>
              <span className="text-xs">{localeNames[locale]}</span>
              {isActive && (
                <svg className="w-3 h-3 text-blue-600 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
