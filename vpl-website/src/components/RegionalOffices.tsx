'use client';

import { useTranslations, useLocale } from 'next-intl';

interface OfficeInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  hours: string;
  wechat?: string;
  qq?: string;
  telegram?: string;
}

export default function RegionalOffices() {
  const t = useTranslations('contact.offices');
  const locale = useLocale();

  // Get region-specific office information
  const getOffices = (): OfficeInfo[] => {
    switch (locale) {
      case 'zh-CN':
      case 'zh-TW':
        return [
          {
            name: t('china.name'),
            address: t('china.address'),
            phone: t('china.phone'),
            email: t('china.email'),
            hours: t('china.hours'),
            wechat: t('china.wechat'),
            qq: t('china.qq'),
          },
          {
            name: t('hongkong.name'),
            address: t('hongkong.address'),
            phone: t('hongkong.phone'),
            email: t('hongkong.email'),
            hours: t('hongkong.hours'),
            wechat: t('hongkong.wechat'),
          },
          {
            name: t('singapore.name'),
            address: t('singapore.address'),
            phone: t('singapore.phone'),
            email: t('singapore.email'),
            hours: t('singapore.hours'),
          },
        ];
      case 'ru':
        return [
          {
            name: t('russia.name'),
            address: t('russia.address'),
            phone: t('russia.phone'),
            email: t('russia.email'),
            hours: t('russia.hours'),
            telegram: t('russia.telegram'),
          },
          {
            name: t('kazakhstan.name'),
            address: t('kazakhstan.address'),
            phone: t('kazakhstan.phone'),
            email: t('kazakhstan.email'),
            hours: t('kazakhstan.hours'),
          },
          {
            name: t('belarus.name'),
            address: t('belarus.address'),
            phone: t('belarus.phone'),
            email: t('belarus.email'),
            hours: t('belarus.hours'),
          },
        ];
      default: // English and fallback
        return [
          {
            name: t('usa.name'),
            address: t('usa.address'),
            phone: t('usa.phone'),
            email: t('usa.email'),
            hours: t('usa.hours'),
          },
          {
            name: t('uk.name'),
            address: t('uk.address'),
            phone: t('uk.phone'),
            email: t('uk.email'),
            hours: t('uk.hours'),
          },
          {
            name: t('singapore.name'),
            address: t('singapore.address'),
            phone: t('singapore.phone'),
            email: t('singapore.email'),
            hours: t('singapore.hours'),
          },
        ];
    }
  };

  const offices = getOffices();

  const getContactMethods = (office: OfficeInfo) => {
    const methods = [];
    
    if (office.wechat) {
      methods.push({
        icon: '💬',
        label: 'WeChat',
        value: office.wechat,
        action: `weixin://dl/chat?${office.wechat}`,
      });
    }
    
    if (office.qq) {
      methods.push({
        icon: '🐧',
        label: 'QQ',
        value: office.qq,
        action: `tencent://message/?uin=${office.qq}`,
      });
    }
    
    if (office.telegram) {
      methods.push({
        icon: '✈️',
        label: 'Telegram',
        value: office.telegram,
        action: `https://t.me/${office.telegram}`,
      });
    }

    return methods;
  };

  return (
    <div className="bg-gray-50 py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center mb-16">
          <h2 className="text-base font-semibold leading-7 text-blue-600">{t('title')}</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            {t('subtitle')}
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            {t('description')}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {offices.map((office, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">{office.name}</h3>
              
              <div className="space-y-4 text-gray-600">
                <div className="flex items-start space-x-3">
                  <span className="text-lg">📍</span>
                  <div>
                    <div className="font-medium text-gray-900">{t('labels.address')}</div>
                    <div>{office.address}</div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="text-lg">📞</span>
                  <div>
                    <div className="font-medium text-gray-900">{t('labels.phone')}</div>
                    <a href={`tel:${office.phone}`} className="text-blue-600 hover:text-blue-800">
                      {office.phone}
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="text-lg">📧</span>
                  <div>
                    <div className="font-medium text-gray-900">{t('labels.email')}</div>
                    <a href={`mailto:${office.email}`} className="text-blue-600 hover:text-blue-800">
                      {office.email}
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="text-lg">🕒</span>
                  <div>
                    <div className="font-medium text-gray-900">{t('labels.hours')}</div>
                    <div>{office.hours}</div>
                  </div>
                </div>

                {/* Region-specific contact methods */}
                {getContactMethods(office).map((method, methodIndex) => (
                  <div key={methodIndex} className="flex items-start space-x-3">
                    <span className="text-lg">{method.icon}</span>
                    <div>
                      <div className="font-medium text-gray-900">{method.label}</div>
                      <a 
                        href={method.action} 
                        className="text-blue-600 hover:text-blue-800"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {method.value}
                      </a>
                    </div>
                  </div>
                ))}
              </div>

              {/* Emergency contact for Chinese regions */}
              {(locale === 'zh-CN' || locale === 'zh-TW') && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center space-x-2">
                    <span className="text-red-600">🚨</span>
                    <div className="text-sm">
                      <div className="font-medium text-red-800">{t('emergency.title')}</div>
                      <div className="text-red-700">{t('emergency.phone')}: {t('emergency.number')}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Business registration info for Russian regions */}
              {locale === 'ru' && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="text-sm">
                    <div className="font-medium text-blue-800">{t('registration.title')}</div>
                    <div className="text-blue-700">{t('registration.info')}</div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Regional payment methods */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">{t('payment.title')}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {(locale === 'zh-CN' || locale === 'zh-TW') && (
              <>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💰</span>
                  <div>
                    <div className="font-medium">{t('payment.alipay')}</div>
                    <div className="text-sm text-gray-600">{t('payment.alipayDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💳</span>
                  <div>
                    <div className="font-medium">{t('payment.wechatPay')}</div>
                    <div className="text-sm text-gray-600">{t('payment.wechatPayDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🏦</span>
                  <div>
                    <div className="font-medium">{t('payment.bankTransfer')}</div>
                    <div className="text-sm text-gray-600">{t('payment.bankTransferDesc')}</div>
                  </div>
                </div>
              </>
            )}
            
            {locale === 'ru' && (
              <>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🏦</span>
                  <div>
                    <div className="font-medium">{t('payment.sberbank')}</div>
                    <div className="text-sm text-gray-600">{t('payment.sberbankDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💳</span>
                  <div>
                    <div className="font-medium">{t('payment.mir')}</div>
                    <div className="text-sm text-gray-600">{t('payment.mirDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">₽</span>
                  <div>
                    <div className="font-medium">{t('payment.cash')}</div>
                    <div className="text-sm text-gray-600">{t('payment.cashDesc')}</div>
                  </div>
                </div>
              </>
            )}
            
            {locale === 'en' && (
              <>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💳</span>
                  <div>
                    <div className="font-medium">{t('payment.creditCard')}</div>
                    <div className="text-sm text-gray-600">{t('payment.creditCardDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🏦</span>
                  <div>
                    <div className="font-medium">{t('payment.wireTransfer')}</div>
                    <div className="text-sm text-gray-600">{t('payment.wireTransferDesc')}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💰</span>
                  <div>
                    <div className="font-medium">{t('payment.paypal')}</div>
                    <div className="text-sm text-gray-600">{t('payment.paypalDesc')}</div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
