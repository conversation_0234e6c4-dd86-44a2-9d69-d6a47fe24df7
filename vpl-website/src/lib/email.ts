// Email service for sending notifications
// Note: In production, you would install nodemailer: npm install nodemailer @types/nodemailer

import { getTranslations } from 'next-intl/server';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface ContactSubmission {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  wechat?: string;
  qq?: string;
  serviceType: string;
  message: string;
  locale?: string;
}

// Default email configuration (should be moved to environment variables)
const DEFAULT_EMAIL_CONFIG: EmailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASS || 'your-app-password',
  },
};

// Mock email service for development
class EmailService {
  private config: EmailConfig;

  constructor(config?: EmailConfig) {
    this.config = config || DEFAULT_EMAIL_CONFIG;
  }

  async sendAdminNotification(submission: ContactSubmission): Promise<boolean> {
    try {
      // In production, use nodemailer to send actual emails
      const emailContent = this.generateAdminNotificationEmail(submission);
      
      // Mock email sending - log to console for development
      console.log('📧 Admin Notification Email:');
      console.log('To:', '<EMAIL>');
      console.log('Subject:', emailContent.subject);
      console.log('Body:', emailContent.html);
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (error) {
      console.error('Error sending admin notification:', error);
      return false;
    }
  }

  async sendCustomerConfirmation(submission: ContactSubmission): Promise<boolean> {
    try {
      const emailContent = this.generateCustomerConfirmationEmail(submission);
      
      // Mock email sending - log to console for development
      console.log('📧 Customer Confirmation Email:');
      console.log('To:', submission.email);
      console.log('Subject:', emailContent.subject);
      console.log('Body:', emailContent.html);
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (error) {
      console.error('Error sending customer confirmation:', error);
      return false;
    }
  }

  private generateAdminNotificationEmail(submission: ContactSubmission) {
    const subject = `New Business Inquiry from ${submission.companyName}`;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Business Inquiry</h2>
        
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #374151;">Company Information</h3>
          <p><strong>Company:</strong> ${submission.companyName}</p>
          <p><strong>Contact Person:</strong> ${submission.contactPerson}</p>
          <p><strong>Email:</strong> ${submission.email}</p>
          <p><strong>Phone:</strong> ${submission.phone}</p>
          ${submission.wechat ? `<p><strong>WeChat:</strong> ${submission.wechat}</p>` : ''}
          ${submission.qq ? `<p><strong>QQ:</strong> ${submission.qq}</p>` : ''}
        </div>
        
        <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #374151;">Service Interest</h3>
          <p><strong>Service Type:</strong> ${submission.serviceType}</p>
        </div>
        
        <div style="background-color: #fefce8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #374151;">Message</h3>
          <p style="white-space: pre-wrap;">${submission.message}</p>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f3f4f6; border-radius: 8px;">
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            This inquiry was submitted through the VPL website contact form.
            Please respond within 24 hours to maintain our service standards.
          </p>
        </div>
      </div>
    `;

    return { subject, html };
  }

  private generateCustomerConfirmationEmail(submission: ContactSubmission) {
    const subject = 'Thank you for your inquiry - VPL Network Solutions';
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; padding: 20px; background-color: #2563eb; color: white; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">VPL Network Solutions</h1>
          <p style="margin: 10px 0 0 0;">Professional Network Solutions & VPN Services</p>
        </div>
        
        <div style="padding: 30px; background-color: #ffffff; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
          <h2 style="color: #374151; margin-top: 0;">Thank you for your inquiry!</h2>
          
          <p>Dear ${submission.contactPerson},</p>
          
          <p>We have received your inquiry regarding <strong>${submission.serviceType}</strong> and appreciate your interest in VPL Network Solutions.</p>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #374151;">What happens next?</h3>
            <ul style="color: #6b7280; line-height: 1.6;">
              <li>Our technical experts will review your requirements</li>
              <li>We will contact you within 24 hours via your preferred method</li>
              <li>We'll schedule a consultation to discuss your specific needs</li>
              <li>You'll receive a customized solution proposal</li>
            </ul>
          </div>
          
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #374151;">Contact Information</h3>
            <p style="margin: 5px 0;"><strong>Email:</strong> <EMAIL></p>
            <p style="margin: 5px 0;"><strong>Phone:</strong> +86-400-VPL-NET</p>
            <p style="margin: 5px 0;"><strong>WeChat:</strong> VPL_Support</p>
            <p style="margin: 5px 0;"><strong>QQ:</strong> 800-888-VPL</p>
          </div>
          
          <p>If you have any urgent questions, please don't hesitate to contact us directly.</p>
          
          <p>Best regards,<br>
          <strong>VPL Network Solutions Team</strong></p>
        </div>
        
        <div style="text-align: center; padding: 20px; color: #6b7280; font-size: 12px;">
          <p>© 2024 VPL Network Solutions. All rights reserved.</p>
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    return { subject, html };
  }

  // Method to update email configuration
  updateConfig(newConfig: Partial<EmailConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Method to test email configuration
  async testConnection(): Promise<boolean> {
    try {
      // In production, test the SMTP connection
      console.log('Testing email configuration...');
      console.log('SMTP Host:', this.config.host);
      console.log('SMTP Port:', this.config.port);
      console.log('SMTP User:', this.config.auth.user);
      
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return true;
    } catch (error) {
      console.error('Email configuration test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
export { EmailService, type EmailConfig, type ContactSubmission };
