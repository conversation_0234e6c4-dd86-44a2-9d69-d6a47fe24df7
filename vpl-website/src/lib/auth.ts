// Authentication utilities for client-side and server-side

export interface User {
  username: string;
  role: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

// Client-side authentication helpers
export const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('adminToken');
  }
  return null;
};

export const setAuthToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('adminToken', token);
  }
};

export const removeAuthToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('adminToken');
  }
};

export const verifyClientAuth = async (): Promise<AuthState> => {
  const token = getAuthToken();
  
  if (!token) {
    return {
      isAuthenticated: false,
      user: null,
      token: null,
    };
  }

  try {
    const response = await fetch('/api/admin/auth', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        isAuthenticated: true,
        user: data.user,
        token,
      };
    } else {
      removeAuthToken();
      return {
        isAuthenticated: false,
        user: null,
        token: null,
      };
    }
  } catch (error) {
    removeAuthToken();
    return {
      isAuthenticated: false,
      user: null,
      token: null,
    };
  }
};

// Server-side token verification
export const verifyServerAuth = (token: string): { username: string; valid: boolean } => {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    const isValid = payload.expires > Date.now();
    return {
      username: payload.username,
      valid: isValid,
    };
  } catch {
    return { username: '', valid: false };
  }
};

export const requireAuth = async (request: Request): Promise<{ authorized: boolean; user?: User }> => {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authorized: false };
  }

  const token = authHeader.substring(7);
  const { username, valid } = verifyServerAuth(token);

  if (!valid) {
    return { authorized: false };
  }

  return {
    authorized: true,
    user: {
      username,
      role: 'admin',
    },
  };
};
