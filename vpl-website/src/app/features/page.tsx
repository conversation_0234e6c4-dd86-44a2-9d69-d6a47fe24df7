const encryptionFeatures = [
  {
    id: 'aes',
    name: 'AES-256 Encryption',
    description: 'Advanced Encryption Standard with 256-bit keys for maximum security',
    details: 'AES-256 is a symmetric encryption algorithm that uses a 256-bit key to encrypt and decrypt data. It is considered one of the most secure encryption methods available and is used by governments and organizations worldwide to protect classified information.',
    benefits: [
      'Military-grade security',
      'Fast encryption/decryption',
      'Widely supported and tested',
      'NSA approved for top secret data',
    ],
    icon: (
      <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
      </svg>
    ),
  },
  {
    id: 'rsa',
    name: 'RSA Asymmetric Encryption',
    description: 'Public-key cryptography for secure key exchange and digital signatures',
    details: 'RSA is an asymmetric cryptographic algorithm that uses a pair of keys - a public key for encryption and a private key for decryption. This enables secure communication without the need to share secret keys beforehand.',
    benefits: [
      'Secure key exchange',
      'Digital signatures',
      'Non-repudiation',
      'Perfect forward secrecy',
    ],
    icon: (
      <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
      </svg>
    ),
  },
  {
    id: 'tls',
    name: 'TLS 1.3 Protocol',
    description: 'Latest Transport Layer Security protocol for secure communication',
    details: 'TLS 1.3 is the latest version of the Transport Layer Security protocol, providing improved security and performance over previous versions. It reduces handshake time and eliminates vulnerable cryptographic algorithms.',
    benefits: [
      'Faster connection establishment',
      'Enhanced security',
      'Reduced latency',
      'Forward secrecy by default',
    ],
    icon: (
      <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0V8.25a1.5 1.5 0 013 0v10.5zM12 18.75a1.5 1.5 0 01-3 0V8.25a1.5 1.5 0 013 0v10.5zM15.75 18.75a1.5 1.5 0 01-3 0V8.25a1.5 1.5 0 013 0v10.5z" />
      </svg>
    ),
  },
  {
    id: 'tunnel',
    name: 'Tunnel Encryption',
    description: 'Secure tunneling protocols to protect data in transit',
    details: 'Tunnel encryption creates a secure "tunnel" through which data can travel safely across public networks. This technology encapsulates and encrypts data packets, making them unreadable to unauthorized parties.',
    benefits: [
      'End-to-end protection',
      'Network layer security',
      'Bypass firewalls safely',
      'Maintain data integrity',
    ],
    icon: (
      <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
      </svg>
    ),
  },
];

const performanceFeatures = [
  {
    name: 'High-Speed Performance',
    description: 'Ultra-fast speeds with minimal latency for optimal user experience',
    metrics: ['Up to 10Gbps bandwidth', 'Sub-50ms latency', '99.9% uptime'],
  },
  {
    name: 'Global Network',
    description: 'Worldwide coverage with strategically located servers',
    metrics: ['50+ global locations', '100+ server nodes', '24/7 monitoring'],
  },
  {
    name: 'Scalable Infrastructure',
    description: 'Easily scale your network capacity as your business grows',
    metrics: ['Auto-scaling', 'Load balancing', 'Redundant systems'],
  },
];

export default function FeaturesPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Advanced <span className="text-blue-600">Security Features</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Cutting-edge encryption technologies and performance features that keep your business secure and connected.
            </p>
          </div>
        </div>
      </div>

      {/* Encryption Features */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center mb-16">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Encryption Technologies</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Military-Grade Security
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our network solutions employ the most advanced encryption technologies to ensure your data remains secure.
            </p>
          </div>

          <div className="space-y-20">
            {encryptionFeatures.map((feature, index) => (
              <div key={feature.id} id={feature.id} className={`lg:grid lg:grid-cols-2 lg:gap-16 lg:items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-600 text-white">
                      {feature.icon}
                    </div>
                    <h3 className="text-2xl font-bold tracking-tight text-gray-900">{feature.name}</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">{feature.details}</p>
                  
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Key Benefits</h4>
                    <ul className="space-y-3">
                      {feature.benefits.map((benefit) => (
                        <li key={benefit} className="flex items-start">
                          <svg className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="text-gray-700">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className={`mt-12 lg:mt-0 ${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-8 h-80 flex items-center justify-center">
                    <div className="text-center">
                      <div className="mx-auto h-20 w-20 text-blue-600 mb-4">
                        {feature.icon}
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-2">{feature.name}</h4>
                      <p className="text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Features */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center mb-16">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Performance & Reliability</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Enterprise-Grade Infrastructure
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Built for speed, reliability, and scale to meet the demands of modern businesses.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {performanceFeatures.map((feature) => (
              <div key={feature.name} className="bg-white rounded-lg shadow-sm p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.name}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.metrics.map((metric) => (
                    <li key={metric} className="flex items-center text-sm text-gray-500">
                      <svg className="h-4 w-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      {metric}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
