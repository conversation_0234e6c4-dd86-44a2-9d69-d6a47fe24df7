import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

import { notFound } from 'next/navigation';
import { locales } from '@/i18n/config';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import StructuredData from "@/components/StructuredData";
import "../globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

// Metadata will be generated dynamically based on locale
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages({ locale });

  // Get SEO translations
  const seo = (messages as any).seo || {};

  return {
    title: seo.title || "VPL - Professional Network Solutions & VPN Services",
    description: seo.description || "VPL provides secure foreign trade network lines, cross-border e-commerce external network lines, and professional VPN services with advanced encryption technologies including AES, RSA, TLS, and tunnel encryption.",
    keywords: seo.keywords || "VPN, network lines, foreign trade, cross-border e-commerce, encryption, AES, RSA, TLS, tunnel encryption, shadowsocks, security",
    openGraph: {
      title: seo.title || "VPL - Professional Network Solutions & VPN Services",
      description: seo.description || "VPL provides secure foreign trade network lines and VPN services",
      type: 'website',
      locale: locale,
      alternateLocale: ['en', 'zh-CN', 'zh-TW', 'ru'].filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.title || "VPL - Professional Network Solutions & VPN Services",
      description: seo.description || "VPL provides secure foreign trade network lines and VPN services",
    },
    alternates: {
      canonical: locale === 'en' ? '/' : `/${locale}`,
      languages: {
        'en': '/',
        'zh-CN': '/zh-CN',
        'zh-TW': '/zh-TW',
        'ru': '/ru',
      },
    },
  };
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // Get messages for the locale
  const messages = await getMessages({ locale });

  return (
    <html lang={locale}>
      <head>
        {/* Hreflang tags for SEO */}
        <link rel="alternate" hrefLang="en" href={`${process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com'}/`} />
        <link rel="alternate" hrefLang="zh-CN" href={`${process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com'}/zh-CN`} />
        <link rel="alternate" hrefLang="zh-TW" href={`${process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com'}/zh-TW`} />
        <link rel="alternate" hrefLang="ru" href={`${process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com'}/ru`} />
        <link rel="alternate" hrefLang="x-default" href={`${process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com'}/`} />
      </head>
      <body className={`${inter.variable} font-sans antialiased bg-white text-gray-900`}>
        <StructuredData type="organization" />
        <StructuredData type="website" />
        <NextIntlClientProvider messages={messages}>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
