import FeaturesSection from '@/components/FeaturesSection';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { getMessages } from 'next-intl/server';
import { Metadata } from 'next';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages({ locale });

  const seo = (messages as any).seo?.features || {};

  return {
    title: seo.title || "VPL Features - Advanced Encryption & Security Technologies",
    description: seo.description || "Advanced security features including AES-256 encryption, RSA asymmetric encryption, TLS 1.3 protocol, tunnel encryption, and Shadowsocks for maximum protection.",
    keywords: seo.keywords || "AES encryption, RSA security, TLS protocol, tunnel encryption, shadowsocks, network security, VPN features",
    openGraph: {
      title: seo.title || "VPL Features - Advanced Encryption & Security Technologies",
      description: seo.description || "Advanced security features and encryption technologies",
      type: 'website',
      locale: locale,
    },
    alternates: {
      canonical: locale === 'en' ? '/features' : `/${locale}/features`,
      languages: {
        'en': '/features',
        'zh-CN': '/zh-CN/features',
        'zh-TW': '/zh-TW/features',
        'ru': '/ru/features',
      },
    },
  };
}

export default function FeaturesPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Advanced <span className="text-blue-600">Features</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Discover the powerful features that make VPL the preferred choice for 
              enterprise network solutions and secure VPN services.
            </p>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <FeaturesSection />

      {/* Technical Specifications */}
      <div className="py-24 sm:py-32 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Technical Specifications</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Enterprise-Grade Performance
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our infrastructure is built to handle the most demanding business requirements 
              with industry-leading performance and reliability.
            </p>
          </div>
          
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
              <div className="flex flex-col">
                <dt className="text-base font-semibold leading-7 text-gray-900">Network Performance</dt>
                <dd className="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <ul className="space-y-2 mt-4">
                    <li>• 99.9% uptime guarantee</li>
                    <li>• Sub-50ms latency globally</li>
                    <li>• 10Gbps+ bandwidth capacity</li>
                    <li>• Load balancing across multiple data centers</li>
                  </ul>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-base font-semibold leading-7 text-gray-900">Security Standards</dt>
                <dd className="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <ul className="space-y-2 mt-4">
                    <li>• AES-256 military-grade encryption</li>
                    <li>• RSA-4096 key exchange</li>
                    <li>• Perfect Forward Secrecy (PFS)</li>
                    <li>• Zero-log policy certified</li>
                  </ul>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-base font-semibold leading-7 text-gray-900">Protocol Support</dt>
                <dd className="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <ul className="space-y-2 mt-4">
                    <li>• OpenVPN (UDP/TCP)</li>
                    <li>• IKEv2/IPSec</li>
                    <li>• Shadowsocks (SS)</li>
                    <li>• WireGuard</li>
                  </ul>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="text-base font-semibold leading-7 text-gray-900">Global Coverage</dt>
                <dd className="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <ul className="space-y-2 mt-4">
                    <li>• 50+ server locations worldwide</li>
                    <li>• Optimized routes for China</li>
                    <li>• Multi-region redundancy</li>
                    <li>• CDN integration available</li>
                  </ul>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600">
        <div className="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to Experience These Features?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
              Contact our team to learn more about how VPL's advanced features 
              can benefit your business operations.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/en/contact"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-blue-600 shadow-sm hover:bg-blue-50"
              >
                Get Started
              </Link>
              <Link href="/en/services" className="text-sm font-semibold leading-6 text-white">
                View Services <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
