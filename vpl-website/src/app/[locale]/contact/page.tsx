import ContactForm from '@/components/ContactForm';
import RegionalOffices from '@/components/RegionalOffices';
import { useTranslations, useLocale } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Metadata } from 'next';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages({ locale });

  const seo = (messages as any).seo?.contact || {};

  return {
    title: seo.title || "Contact VPL - Get Professional Network Solutions",
    description: seo.description || "Contact VPL for professional network solutions, VPN services, and business consultation. Multiple contact methods including WeChat, QQ, phone, and email support.",
    keywords: seo.keywords || "contact VPL, network consultation, VPN support, business inquiry, WeChat support, QQ support",
    openGraph: {
      title: seo.title || "Contact VPL - Get Professional Network Solutions",
      description: seo.description || "Contact VPL for professional network solutions and consultation",
      type: 'website',
      locale: locale,
    },
    alternates: {
      canonical: locale === 'en' ? '/contact' : `/${locale}/contact`,
      languages: {
        'en': '/contact',
        'zh-CN': '/zh-CN/contact',
        'zh-TW': '/zh-TW/contact',
        'ru': '/ru/contact',
      },
    },
  };
}

export default function ContactPage() {
  const t = useTranslations('contact');
  const locale = useLocale();
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              {t('hero.title')} <span className="text-blue-600">VPL</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              {t('hero.subtitle')}
            </p>
          </div>
        </div>
      </div>

      {/* Contact Form Section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <ContactForm />
        </div>
      </div>

      {/* Regional Offices */}
      <RegionalOffices />

      {/* Contact Information */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Contact Information</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Multiple Ways to Reach Us
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Choose your preferred communication method. We support various platforms 
              popular in different regions.
            </p>
          </div>
          
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z" clipRule="evenodd" />
                  </svg>
                  Phone & SMS
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">Direct phone support and SMS communication for urgent inquiries.</p>
                  <p className="mt-6">
                    <a href="tel:+86-400-VPL-NET" className="text-sm font-semibold leading-6 text-blue-600">
                      +86-400-VPL-NET <span aria-hidden="true">→</span>
                    </a>
                  </p>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M3 4a2 2 0 00-2 2v1.161l8.441 4.221a1.25 1.25 0 001.118 0L19 7.162V6a2 2 0 00-2-2H3z" />
                    <path d="M19 8.839l-7.77 3.885a2.75 2.75 0 01-2.46 0L1 8.839V14a2 2 0 002 2h14a2 2 0 002-2V8.839z" />
                  </svg>
                  Email
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">Professional email support for detailed inquiries and documentation.</p>
                  <p className="mt-6">
                    <a href="mailto:<EMAIL>" className="text-sm font-semibold leading-6 text-blue-600">
                      <EMAIL> <span aria-hidden="true">→</span>
                    </a>
                  </p>
                </dd>
              </div>
              
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.5c2.43 0 4.817.178 7.152.271 1.016.04 1.848.85 1.848 1.87v11.717c0 1.02-.832 1.83-1.848 1.87A49.144 49.144 0 0112 17.5c-2.43 0-4.817-.178-7.152-.271-1.016-.04-1.848-.85-1.848-1.87V4.642c0-1.02.832-1.83 1.848-1.87z" clipRule="evenodd" />
                  </svg>
                  WeChat & QQ
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">Popular messaging platforms for real-time communication in China.</p>
                  <div className="mt-6 space-y-2">
                    <p className="text-sm font-semibold leading-6 text-blue-600">
                      WeChat: VPL_Support
                    </p>
                    <p className="text-sm font-semibold leading-6 text-blue-600">
                      QQ: 800-888-VPL
                    </p>
                  </div>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
}
