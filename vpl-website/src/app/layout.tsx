import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "VPL - Professional Network Solutions & VPN Services",
  description: "VPL provides secure foreign trade network lines, cross-border e-commerce external network lines, and professional VPN services with advanced encryption technologies including AES, RSA, TLS, and tunnel encryption.",
  keywords: "VPN, network lines, foreign trade, cross-border e-commerce, encryption, AES, RSA, TLS, tunnel encryption, shadowsocks, security",
};

// This layout only applies to the root page
// All other pages use the [locale]/layout.tsx
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return children;
}
