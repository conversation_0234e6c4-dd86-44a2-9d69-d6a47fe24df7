import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "VPL - Professional Network Solutions & VPN Services",
  description: "VPL provides secure foreign trade network lines, cross-border e-commerce external network lines, and professional VPN services with advanced encryption technologies including AES, RSA, TLS, and tunnel encryption.",
  keywords: "VPN, network lines, foreign trade, cross-border e-commerce, encryption, AES, RSA, TLS, tunnel encryption, shadowsocks, security",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-white text-gray-900`}>
        {children}
      </body>
    </html>
  );
}
