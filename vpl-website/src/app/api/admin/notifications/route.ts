import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';

// Simple in-memory store for active connections
const connections = new Set<ReadableStreamDefaultController>();

// Notification types
interface Notification {
  id: string;
  type: 'new_contact' | 'status_update' | 'system';
  title: string;
  message: string;
  timestamp: string;
  data?: any;
}

// Function to broadcast notification to all connected clients
export function broadcastNotification(notification: Notification) {
  const message = `data: ${JSON.stringify(notification)}\n\n`;
  
  connections.forEach((controller) => {
    try {
      controller.enqueue(new TextEncoder().encode(message));
    } catch (error) {
      // Remove dead connections
      connections.delete(controller);
    }
  });
}

export async function GET(request: NextRequest) {
  // Check authentication
  const auth = await requireAuth(request);
  if (!auth.authorized) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      // Add this connection to our set
      connections.add(controller);

      // Send initial connection message
      const welcomeMessage = {
        id: Date.now().toString(),
        type: 'system' as const,
        title: 'Connected',
        message: 'Real-time notifications connected',
        timestamp: new Date().toISOString(),
      };

      controller.enqueue(
        new TextEncoder().encode(`data: ${JSON.stringify(welcomeMessage)}\n\n`)
      );

      // Send keep-alive ping every 30 seconds
      const keepAlive = setInterval(() => {
        try {
          controller.enqueue(new TextEncoder().encode(': keep-alive\n\n'));
        } catch (error) {
          clearInterval(keepAlive);
          connections.delete(controller);
        }
      }, 30000);

      // Clean up when connection closes
      return () => {
        clearInterval(keepAlive);
        connections.delete(controller);
      };
    },
    cancel() {
      // Connection was closed by client
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

// Helper function to create notifications
export function createNotification(
  type: Notification['type'],
  title: string,
  message: string,
  data?: any
): Notification {
  return {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    type,
    title,
    message,
    timestamp: new Date().toISOString(),
    data,
  };
}

// Export for use in other API routes
export { type Notification };
