import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Simple in-memory storage for demo purposes
// In production, use a proper database and secure password hashing
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'vpl2024', // In production, this should be hashed
};

// Simple JWT-like token generation (for demo purposes)
const generateToken = (username: string): string => {
  const payload = {
    username,
    timestamp: Date.now(),
    expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
  };
  
  // In production, use a proper JWT library with secret key
  return Buffer.from(JSON.stringify(payload)).toString('base64');
};

const verifyToken = (token: string): { username: string; valid: boolean } => {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    const isValid = payload.expires > Date.now();
    return {
      username: payload.username,
      valid: isValid,
    };
  } catch {
    return { username: '', valid: false };
  }
};

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    // Validate credentials
    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
      const token = generateToken(username);
      
      return NextResponse.json({
        success: true,
        token,
        user: {
          username,
          role: 'admin',
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request' },
      { status: 400 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const { username, valid } = verifyToken(token);

    if (!valid) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      valid: true,
      user: {
        username,
        role: 'admin',
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Token verification failed' },
      { status: 401 }
    );
  }
}

// Export the verifyToken function for use in other API routes
export { verifyToken };
