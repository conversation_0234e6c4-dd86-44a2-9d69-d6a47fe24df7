import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import fs from 'fs';
import path from 'path';

interface ContactSubmission {
  id: string;
  timestamp: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  wechat?: string;
  qq?: string;
  serviceType: string;
  message: string;
  status: 'new' | 'contacted' | 'closed';
  notes?: string;
}

const getDataFilePath = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  return path.join(dataDir, 'contacts.json');
};

const loadContacts = (): ContactSubmission[] => {
  try {
    const filePath = getDataFilePath();
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading contacts:', error);
  }
  return [];
};

const saveContacts = (contacts: ContactSubmission[]) => {
  try {
    const filePath = getDataFilePath();
    fs.writeFileSync(filePath, JSON.stringify(contacts, null, 2));
  } catch (error) {
    console.error('Error saving contacts:', error);
    throw error;
  }
};

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await requireAuth(request);
    if (!auth.authorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { status, notes } = body;

    // Validate status
    if (!['new', 'contacted', 'closed'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Load contacts
    const contacts = loadContacts();
    const contactIndex = contacts.findIndex(contact => contact.id === id);

    if (contactIndex === -1) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Update contact
    contacts[contactIndex] = {
      ...contacts[contactIndex],
      status,
      notes: notes || contacts[contactIndex].notes,
    };

    // Save updated contacts
    saveContacts(contacts);

    return NextResponse.json({
      success: true,
      contact: contacts[contactIndex],
    });

  } catch (error) {
    console.error('Error updating contact:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await requireAuth(request);
    if (!auth.authorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const contacts = loadContacts();
    const contact = contacts.find(contact => contact.id === id);

    if (!contact) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(contact);

  } catch (error) {
    console.error('Error fetching contact:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
