import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { emailService } from '@/lib/email';
import { broadcastNotification, createNotification } from '@/app/api/admin/notifications/route';

interface ContactSubmission {
  id: string;
  timestamp: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  wechat?: string;
  qq?: string;
  serviceType: string;
  message: string;
  status: 'new' | 'contacted' | 'closed';
  notes?: string;
}

// Simple file-based storage for development
const getDataFilePath = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  return path.join(dataDir, 'contacts.json');
};

const loadContacts = (): ContactSubmission[] => {
  try {
    const filePath = getDataFilePath();
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading contacts:', error);
  }
  return [];
};

const saveContacts = (contacts: ContactSubmission[]) => {
  try {
    const filePath = getDataFilePath();
    fs.writeFileSync(filePath, JSON.stringify(contacts, null, 2));
  } catch (error) {
    console.error('Error saving contacts:', error);
    throw error;
  }
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['companyName', 'contactPerson', 'email', 'phone', 'serviceType', 'message'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Create new contact submission
    const newContact: ContactSubmission = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      companyName: body.companyName,
      contactPerson: body.contactPerson,
      email: body.email,
      phone: body.phone,
      wechat: body.wechat || '',
      qq: body.qq || '',
      serviceType: body.serviceType,
      message: body.message,
      status: 'new',
    };

    // Load existing contacts and add new one
    const contacts = loadContacts();
    contacts.push(newContact);
    saveContacts(contacts);

    // Send email notifications
    try {
      // Send admin notification
      await emailService.sendAdminNotification(newContact);

      // Send customer confirmation
      await emailService.sendCustomerConfirmation(newContact);

      console.log('Email notifications sent successfully');
    } catch (error) {
      console.error('Error sending email notifications:', error);
      // Don't fail the request if email fails
    }

    // Send real-time notification to admin dashboard
    try {
      const notification = createNotification(
        'new_contact',
        'New Business Inquiry',
        `New inquiry from ${newContact.companyName} - ${newContact.contactPerson}`,
        {
          contactId: newContact.id,
          companyName: newContact.companyName,
          serviceType: newContact.serviceType,
        }
      );

      broadcastNotification(notification);
      console.log('Real-time notification sent');
    } catch (error) {
      console.error('Error sending real-time notification:', error);
      // Don't fail the request if notification fails
    }

    console.log('New contact submission:', newContact);

    return NextResponse.json(
      { 
        message: 'Contact form submitted successfully',
        id: newContact.id 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error processing contact form:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // This endpoint will be used by the admin dashboard
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    
    let contacts = loadContacts();
    
    // Filter by status if provided
    if (status && status !== 'all') {
      contacts = contacts.filter(contact => contact.status === status);
    }
    
    // Sort by timestamp (newest first)
    contacts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return NextResponse.json(contacts);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
