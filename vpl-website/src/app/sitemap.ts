import { MetadataRoute } from 'next';
import { locales } from '@/i18n/config';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://vpl.com';
  
  // Define all pages that should be included in sitemap
  const pages = [
    '',
    '/services',
    '/features',
    '/contact',
  ];

  const sitemap: MetadataRoute.Sitemap = [];

  // Generate sitemap entries for each locale and page
  locales.forEach((locale) => {
    pages.forEach((page) => {
      const url = locale === 'en' 
        ? `${baseUrl}${page}` 
        : `${baseUrl}/${locale}${page}`;
      
      // Create alternate language links
      const alternates: { [key: string]: string } = {};
      locales.forEach((altLocale) => {
        const altUrl = altLocale === 'en' 
          ? `${baseUrl}${page}` 
          : `${baseUrl}/${altLocale}${page}`;
        alternates[altLocale] = altUrl;
      });

      sitemap.push({
        url,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: page === '' ? 1.0 : 0.8,
        alternates: {
          languages: alternates,
        },
      });
    });
  });

  return sitemap;
}
