'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { verifyClientAuth, removeAuthToken, type AuthState } from '@/lib/auth';
import NotificationSystem from '@/components/NotificationSystem';

interface ContactSubmission {
  id: string;
  timestamp: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  wechat?: string;
  qq?: string;
  serviceType: string;
  message: string;
  status: 'new' | 'contacted' | 'closed';
  notes?: string;
}

export default function AdminDashboard() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
  });
  const [contacts, setContacts] = useState<ContactSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      const auth = await verifyClientAuth();
      if (!auth.isAuthenticated) {
        router.push('/admin/login');
        return;
      }
      setAuthState(auth);
      setLoading(false);
    };

    checkAuth();
  }, [router]);

  useEffect(() => {
    if (authState.isAuthenticated) {
      fetchContacts();
    }
  }, [authState.isAuthenticated, selectedStatus]);

  const fetchContacts = async () => {
    try {
      const response = await fetch(`/api/contact?status=${selectedStatus}`, {
        headers: {
          'Authorization': `Bearer ${authState.token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setContacts(data);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    }
  };

  const handleLogout = () => {
    removeAuthToken();
    router.push('/admin/login');
  };

  const updateContactStatus = async (id: string, status: 'new' | 'contacted' | 'closed') => {
    try {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authState.token}`,
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        fetchContacts(); // Refresh the list
      }
    } catch (error) {
      console.error('Error updating contact status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-red-100 text-red-800';
      case 'contacted':
        return 'bg-yellow-100 text-yellow-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Notification System */}
      <NotificationSystem
        token={authState.token}
        onNewContact={fetchContacts}
      />

      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">V</span>
                </div>
                <span className="text-2xl font-bold text-gray-900">VPL Admin</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {authState.user?.username}</span>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {contacts.filter(c => c.status === 'new').length}
                      </span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">New Inquiries</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {contacts.filter(c => c.status === 'new').length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-600 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {contacts.filter(c => c.status === 'contacted').length}
                      </span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {contacts.filter(c => c.status === 'contacted').length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {contacts.filter(c => c.status === 'closed').length}
                      </span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {contacts.filter(c => c.status === 'closed').length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg leading-6 font-medium text-gray-900">Contact Submissions</h3>
                <div className="flex space-x-2">
                  {['all', 'new', 'contacted', 'closed'].map((status) => (
                    <button
                      key={status}
                      onClick={() => setSelectedStatus(status)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        selectedStatus === status
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Contact List */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {contacts.map((contact) => (
                <li key={contact.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-blue-600 truncate">
                            {contact.companyName} - {contact.contactPerson}
                          </p>
                          <div className="ml-2 flex-shrink-0 flex">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(contact.status)}`}>
                              {contact.status}
                            </span>
                          </div>
                        </div>
                        <div className="mt-2 sm:flex sm:justify-between">
                          <div className="sm:flex">
                            <p className="flex items-center text-sm text-gray-500">
                              {contact.email} | {contact.phone}
                            </p>
                          </div>
                          <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                            <p>{formatDate(contact.timestamp)}</p>
                          </div>
                        </div>
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">Service: {contact.serviceType}</p>
                          <p className="text-sm text-gray-600 mt-1">{contact.message}</p>
                        </div>
                        <div className="mt-3 flex space-x-2">
                          {contact.status !== 'contacted' && (
                            <button
                              onClick={() => updateContactStatus(contact.id, 'contacted')}
                              className="text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700"
                            >
                              Mark as Contacted
                            </button>
                          )}
                          {contact.status !== 'closed' && (
                            <button
                              onClick={() => updateContactStatus(contact.id, 'closed')}
                              className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                            >
                              Mark as Closed
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
            {contacts.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500">No contact submissions found.</p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
