'use client';

import { useState } from 'react';
import ContactForm from '@/components/ContactForm';

const contactMethods = [
  {
    name: 'WeChat',
    description: 'Connect with us on WeChat for instant messaging support',
    icon: (
      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 01.213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 00.167-.054l1.903-1.114a.864.864 0 01.717-.098 10.16 10.16 0 002.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55 1.545.781 2.765 2.205 3.429 4.006.745 2.023.688 4.361-.202 6.348-.890 1.987-2.312 3.334-4.1 3.881a5.862 5.862 0 01-1.2.12c-.26 0-.52-.02-.78-.059L8.691 2.188z"/>
      </svg>
    ),
    contact: 'VPL_Support',
    action: 'Add WeChat',
  },
  {
    name: 'QQ',
    description: 'Reach us through QQ for comprehensive business discussions',
    icon: (
      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 14.832c-.126.256-.378.458-.68.458-.302 0-.554-.202-.68-.458a6.262 6.262 0 01-.888-3.166c0-1.078.27-2.094.748-2.98.126-.256.378-.458.68-.458.302 0 .554.202.68.458.478.886.748 1.902.748 2.98 0 1.078-.27 2.094-.608 3.166z"/>
      </svg>
    ),
    contact: '800-888-VPL',
    action: 'Add QQ',
  },
  {
    name: 'Email',
    description: 'Send us detailed inquiries via email for comprehensive responses',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
      </svg>
    ),
    contact: '<EMAIL>',
    action: 'Send Email',
  },
  {
    name: 'Phone',
    description: 'Call us directly for immediate assistance and consultation',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
      </svg>
    ),
    contact: '+86-400-VPL-NET',
    action: 'Call Now',
  },
];

export default function ContactPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Get in <span className="text-blue-600">Touch</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Ready to secure your business network? Contact our experts for a free consultation and custom solution design.
            </p>
          </div>
        </div>
      </div>

      {/* Contact Methods */}
      <div className="py-24 sm:py-32 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center mb-16">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Multiple Ways to Connect</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Choose Your Preferred Communication Method
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              We offer various communication channels to ensure you can reach us in the way that works best for you.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {contactMethods.map((method) => (
              <div key={method.name} className="bg-white rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-600 text-white mb-4">
                  {method.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{method.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{method.description}</p>
                <p className="text-sm font-medium text-blue-600 mb-4">{method.contact}</p>
                <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                  {method.action}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Form Section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center mb-16">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Business Consultation Form</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Request a Free Consultation
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Fill out the form below and our experts will contact you within 24 hours to discuss your network requirements.
            </p>
          </div>

          <div className="mx-auto max-w-3xl">
            <ContactForm />
          </div>
        </div>
      </div>

      {/* Office Information */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center mb-16">
            <h2 className="text-base font-semibold leading-7 text-blue-600">Our Offices</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Global Presence, Local Support
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Asia Pacific Headquarters</h3>
              <div className="space-y-3 text-gray-600">
                <p>📍 Shanghai, China</p>
                <p>📞 +86-21-XXXX-XXXX</p>
                <p>📧 <EMAIL></p>
                <p>🕒 Mon-Fri: 9:00-18:00 CST</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">North America Office</h3>
              <div className="space-y-3 text-gray-600">
                <p>📍 San Francisco, USA</p>
                <p>📞 +1-415-XXX-XXXX</p>
                <p>📧 <EMAIL></p>
                <p>🕒 Mon-Fri: 9:00-17:00 PST</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Europe Office</h3>
              <div className="space-y-3 text-gray-600">
                <p>📍 London, UK</p>
                <p>📞 +44-20-XXXX-XXXX</p>
                <p>📧 <EMAIL></p>
                <p>🕒 Mon-Fri: 9:00-17:00 GMT</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
