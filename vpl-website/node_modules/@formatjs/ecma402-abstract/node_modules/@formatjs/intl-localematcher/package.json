{"name": "@formatjs/intl-localematcher", "description": "Intl.LocaleMatcher ponyfill", "version": "0.6.1", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "types": "index.d.ts", "dependencies": {"tslib": "^2.8.0"}, "bugs": "https://github.com/formatjs/formatjs/issues", "homepage": "https://github.com/formatjs/formatjs#readme", "keywords": ["ecma402", "formatjs", "i18n", "intl", "locale", "react-intl", "tc39"], "main": "index.js", "module": "lib/index.js", "repository": "formatjs/formatjs.git"}