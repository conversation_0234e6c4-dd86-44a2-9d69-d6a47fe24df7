"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isStructurallySame = exports._Parser = void 0;
exports.parse = parse;
var tslib_1 = require("tslib");
function parse() {
    throw new Error("You're trying to format an uncompiled message with react-intl without parser, please import from 'react-intl' instead");
}
tslib_1.__exportStar(require("./types"), exports);
exports._Parser = undefined;
var manipulator_1 = require("./manipulator");
Object.defineProperty(exports, "isStructurallySame", { enumerable: true, get: function () { return manipulator_1.isStructurallySame; } });
