{"name": "use-intl", "version": "4.3.4", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "description": "Internationalization (i18n) for React", "license": "MIT", "homepage": "https://next-intl.dev", "repository": {"type": "git", "url": "https://github.com/amannn/next-intl/tree/main/packages/use-intl"}, "scripts": {"build": "rm -rf dist && rollup -c", "test": "TZ=Europe/Berlin vitest", "lint": "pnpm run lint:source && pnpm run lint:package", "lint:source": "eslint src test && tsc --noEmit && pnpm run lint:prettier", "lint:package": "publint && attw --pack --ignore-rules=cjs-resolves-to-esm", "lint:prettier": "prettier src --check", "prepublishOnly": "turbo build", "size": "size-limit"}, "type": "module", "main": "./dist/esm/production/index.js", "typings": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "development": "./dist/esm/development/index.js", "default": "./dist/esm/production/index.js"}, "./core": {"types": "./dist/types/core.d.ts", "development": "./dist/esm/development/core.js", "default": "./dist/esm/production/core.js"}, "./react": {"types": "./dist/types/react.d.ts", "development": "./dist/esm/development/react.js", "default": "./dist/esm/production/react.js"}}, "files": ["dist", "core.d.ts", "react.d.ts"], "keywords": ["react", "intl", "i18n", "internationalization", "localization", "translate", "translation", "format", "formatting"], "dependencies": {"@formatjs/fast-memoize": "^2.2.0", "@schummar/icu-type-parser": "1.21.5", "intl-messageformat": "^10.5.14"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0"}, "gitHead": "44f07b236ad9b1a9d6e99e55507e7a67939efe5a"}