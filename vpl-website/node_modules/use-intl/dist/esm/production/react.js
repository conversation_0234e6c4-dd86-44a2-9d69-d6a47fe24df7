import{createContext as e,useContext as r,use<PERSON>emo as o,useState as t,useEffect as n}from"react";import{d as a,b as s,i as c,r as i,I as m,a as f,e as l,c as u}from"./initializeConfig-JxT5lzW-.js";import{jsx as g}from"react/jsx-runtime";const d=e(void 0);function w({children:e,formats:t,getMessageFallback:n,locale:i,messages:m,now:f,onError:l,timeZone:u}){const w=r(d),v=o((()=>w?.cache||a()),[i,w?.cache]),E=o((()=>w?.formatters||s(v)),[v,w?.formatters]),h=o((()=>({...c({locale:i,formats:void 0===t?w?.formats:t,getMessageFallback:n||w?.getMessageFallback,messages:void 0===m?w?.messages:m,now:f||w?.now,onError:l||w?.onError,timeZone:u||w?.timeZone}),formatters:E,cache:v})),[v,t,E,n,i,m,f,l,w,u]);return g(d.Provider,{value:h,children:e})}function v(){const e=r(d);if(!e)throw new Error(void 0);return e}let E=!1;const h="undefined"==typeof window;function p(e){return function(e,r,t){const{cache:n,formats:a,formatters:s,getMessageFallback:c,locale:u,onError:g,timeZone:d}=v(),w=e[t],p=i(r,t);return d||E||!h||(E=!0,g(new m(f.ENVIRONMENT_FALLBACK,void 0))),o((()=>l({cache:n,formatters:s,getMessageFallback:c,messages:w,namespace:p,onError:g,formats:a,locale:u,timeZone:d})),[n,s,c,w,p,g,a,u,d])}({"!":v().messages},e?`!.${e}`:"!","!")}function Z(){return v().locale}function b(){return new Date}function F(e){const r=e?.updateInterval,{now:o}=v(),[a,s]=t(o||b());return n((()=>{if(!r)return;const e=setInterval((()=>{s(b())}),r);return()=>{clearInterval(e)}}),[o,r]),null==r&&o?o:a}function M(){return v().timeZone}function k(){const e=v();if(!e.messages)throw new Error(void 0);return e.messages}function I(){const{formats:e,formatters:r,locale:t,now:n,onError:a,timeZone:s}=v();return o((()=>u({formats:e,locale:t,now:n,onError:a,timeZone:s,_formatters:r})),[e,r,n,t,a,s])}export{w as IntlProvider,I as useFormatter,Z as useLocale,k as useMessages,F as useNow,M as useTimeZone,p as useTranslations};
