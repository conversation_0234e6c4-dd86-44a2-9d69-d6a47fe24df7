import{usePathname as e}from"next/navigation";import{use<PERSON>emo as r}from"react";import{useLocale as o}from"use-intl";import{hasPathnamePrefixed as t,unprefixPathname as i,getLocalePrefix as f,getLocaleAsPrefix as l}from"../../shared/utils.js";function n(n){const s=e(),a=o();return r((()=>{if(!s)return s;let e=s;const r=f(a,n.localePrefix);if(t(r,s))e=i(s,r);else if("as-needed"===n.localePrefix.mode&&n.localePrefix.prefixes){const r=l(a);t(r,s)&&(e=i(s,r))}return e}),[n.localePrefix,a,s])}export{n as default};
