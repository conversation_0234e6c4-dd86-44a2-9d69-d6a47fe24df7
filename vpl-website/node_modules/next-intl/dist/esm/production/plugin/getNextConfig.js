import t from"fs";import e from"path";import n from"./hasStableTurboConfig.js";import{throwError as o}from"./utils.js";function r(t){return[`${t}.ts`,`${t}.tsx`,`${t}.js`,`${t}.jsx`]}function s(n,s){function i(n){return t.existsSync(function(t){const n=[];return s&&n.push(s),n.push(t),e.resolve(...n)}(n))}if(n)return i(n)||o(`Could not find i18n config at ${n}, please provide a valid path.`),n;for(const t of[...r("./i18n/request"),...r("./src/i18n/request")])if(i(t))return t;o("Could not locate request configuration module.\n\nThis path is supported by default: ./(src/)i18n/request.{js,jsx,ts,tsx}\n\nAlternatively, you can specify a custom location in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n\nAlternatively, you can specify a custom location in your Next.js config:\n\nconst withNextIntl = createNextIntlPlugin(\n  './path/to/i18n/request.tsx'\n);")}function i(t,r){const i={};if(null!=process.env.TURBOPACK){t.requestConfig?.startsWith("/")&&o("Turbopack support for next-intl currently does not support absolute paths, please provide a relative one (e.g. './src/i18n/config.ts').\n\nFound: "+t.requestConfig);const e={"next-intl/config":s(t.requestConfig)};n&&!r?.experimental?.turbo?i.turbopack={...r?.turbopack,resolveAlias:{...r?.turbopack?.resolveAlias,...e}}:i.experimental={...r?.experimental,turbo:{...r?.experimental?.turbo,resolveAlias:{...r?.experimental?.turbo?.resolveAlias,...e}}}}else i.webpack=function(...[n,o]){return n.resolve.alias["next-intl/config"]=e.resolve(n.context,s(t.requestConfig,n.context)),"function"==typeof r?.webpack?r.webpack(n,o):n};return r?.trailingSlash&&(i.env={...r.env,_next_intl_trailing_slash:"true"}),Object.assign({},r,i)}export{i as default};
