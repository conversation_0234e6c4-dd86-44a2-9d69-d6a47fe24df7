import type { RoutingConfigLocalizedNavigation, RoutingConfigSharedNavigation } from '../../routing/config.js';
import type { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../../routing/types.js';
export default function createNavigation<const AppLocales extends Locales, const AppLocalePrefixMode extends LocalePrefixMode = 'always', const AppPathnames extends Pathnames<AppLocales> = never, const AppDomains extends DomainsConfig<AppLocales> = never>(routing?: [AppPathnames] extends [never] ? RoutingConfigSharedNavigation<AppLocales, AppLocalePrefixMode, AppDomains> | undefined : RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>): {
    usePathname: () => never;
    useRouter: () => never;
    Link: import("react").ForwardRefExoticComponent<Omit<{
        slot?: string | undefined | undefined;
        style?: import("react").CSSProperties | undefined;
        title?: string | undefined | undefined;
        locale?: import("use-intl").Locale | undefined;
        onError?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        children?: import("react").ReactNode;
        ref?: import("react").Ref<HTMLAnchorElement> | undefined;
        replace?: boolean | undefined;
        prefix?: string | undefined | undefined;
        key?: import("react").Key | null | undefined;
        as?: (string | import("url").UrlObject) | undefined;
        scroll?: boolean | undefined;
        shallow?: boolean | undefined;
        passHref?: boolean | undefined;
        prefetch?: boolean | null | undefined;
        legacyBehavior?: boolean | undefined;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement> | undefined;
        onClick?: React.MouseEventHandler<HTMLAnchorElement> | undefined;
        onNavigate?: ((event: {
            preventDefault: () => void;
        }) => void) | undefined;
        download?: any;
        hrefLang?: string | undefined | undefined;
        media?: string | undefined | undefined;
        ping?: string | undefined | undefined;
        target?: import("react").HTMLAttributeAnchorTarget | undefined;
        type?: string | undefined | undefined;
        referrerPolicy?: import("react").HTMLAttributeReferrerPolicy | undefined;
        defaultChecked?: boolean | undefined | undefined;
        defaultValue?: string | number | readonly string[] | undefined;
        suppressContentEditableWarning?: boolean | undefined | undefined;
        suppressHydrationWarning?: boolean | undefined | undefined;
        accessKey?: string | undefined | undefined;
        autoCapitalize?: "off" | "none" | "on" | "sentences" | "words" | "characters" | undefined | (string & {}) | undefined;
        autoFocus?: boolean | undefined | undefined;
        className?: string | undefined | undefined;
        contentEditable?: (boolean | "true" | "false") | "inherit" | "plaintext-only" | undefined;
        contextMenu?: string | undefined | undefined;
        dir?: string | undefined | undefined;
        draggable?: (boolean | "true" | "false") | undefined;
        enterKeyHint?: "enter" | "done" | "go" | "next" | "previous" | "search" | "send" | undefined | undefined;
        hidden?: boolean | undefined | undefined;
        id?: string | undefined | undefined;
        lang?: string | undefined | undefined;
        nonce?: string | undefined | undefined;
        spellCheck?: (boolean | "true" | "false") | undefined;
        tabIndex?: number | undefined | undefined;
        translate?: "yes" | "no" | undefined | undefined;
        radioGroup?: string | undefined | undefined;
        role?: import("react").AriaRole | undefined;
        about?: string | undefined | undefined;
        content?: string | undefined | undefined;
        datatype?: string | undefined | undefined;
        inlist?: any;
        property?: string | undefined | undefined;
        rel?: string | undefined | undefined;
        resource?: string | undefined | undefined;
        rev?: string | undefined | undefined;
        typeof?: string | undefined | undefined;
        vocab?: string | undefined | undefined;
        autoCorrect?: string | undefined | undefined;
        autoSave?: string | undefined | undefined;
        color?: string | undefined | undefined;
        itemProp?: string | undefined | undefined;
        itemScope?: boolean | undefined | undefined;
        itemType?: string | undefined | undefined;
        itemID?: string | undefined | undefined;
        itemRef?: string | undefined | undefined;
        results?: number | undefined | undefined;
        security?: string | undefined | undefined;
        unselectable?: "on" | "off" | undefined | undefined;
        popover?: "" | "auto" | "manual" | undefined | undefined;
        popoverTargetAction?: "toggle" | "show" | "hide" | undefined | undefined;
        popoverTarget?: string | undefined | undefined;
        inert?: boolean | undefined | undefined;
        inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined | undefined;
        is?: string | undefined | undefined;
        tw?: string | undefined;
        "aria-activedescendant"?: string | undefined | undefined;
        "aria-atomic"?: (boolean | "true" | "false") | undefined;
        "aria-autocomplete"?: "none" | "inline" | "list" | "both" | undefined | undefined;
        "aria-braillelabel"?: string | undefined | undefined;
        "aria-brailleroledescription"?: string | undefined | undefined;
        "aria-busy"?: (boolean | "true" | "false") | undefined;
        "aria-checked"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
        "aria-colcount"?: number | undefined | undefined;
        "aria-colindex"?: number | undefined | undefined;
        "aria-colindextext"?: string | undefined | undefined;
        "aria-colspan"?: number | undefined | undefined;
        "aria-controls"?: string | undefined | undefined;
        "aria-current"?: boolean | "false" | "true" | "page" | "step" | "location" | "date" | "time" | undefined | undefined;
        "aria-describedby"?: string | undefined | undefined;
        "aria-description"?: string | undefined | undefined;
        "aria-details"?: string | undefined | undefined;
        "aria-disabled"?: (boolean | "true" | "false") | undefined;
        "aria-dropeffect"?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined | undefined;
        "aria-errormessage"?: string | undefined | undefined;
        "aria-expanded"?: (boolean | "true" | "false") | undefined;
        "aria-flowto"?: string | undefined | undefined;
        "aria-grabbed"?: (boolean | "true" | "false") | undefined;
        "aria-haspopup"?: boolean | "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined | undefined;
        "aria-hidden"?: (boolean | "true" | "false") | undefined;
        "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined | undefined;
        "aria-keyshortcuts"?: string | undefined | undefined;
        "aria-label"?: string | undefined | undefined;
        "aria-labelledby"?: string | undefined | undefined;
        "aria-level"?: number | undefined | undefined;
        "aria-live"?: "off" | "assertive" | "polite" | undefined | undefined;
        "aria-modal"?: (boolean | "true" | "false") | undefined;
        "aria-multiline"?: (boolean | "true" | "false") | undefined;
        "aria-multiselectable"?: (boolean | "true" | "false") | undefined;
        "aria-orientation"?: "horizontal" | "vertical" | undefined | undefined;
        "aria-owns"?: string | undefined | undefined;
        "aria-placeholder"?: string | undefined | undefined;
        "aria-posinset"?: number | undefined | undefined;
        "aria-pressed"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
        "aria-readonly"?: (boolean | "true" | "false") | undefined;
        "aria-relevant"?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined | undefined;
        "aria-required"?: (boolean | "true" | "false") | undefined;
        "aria-roledescription"?: string | undefined | undefined;
        "aria-rowcount"?: number | undefined | undefined;
        "aria-rowindex"?: number | undefined | undefined;
        "aria-rowindextext"?: string | undefined | undefined;
        "aria-rowspan"?: number | undefined | undefined;
        "aria-selected"?: (boolean | "true" | "false") | undefined;
        "aria-setsize"?: number | undefined | undefined;
        "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined | undefined;
        "aria-valuemax"?: number | undefined | undefined;
        "aria-valuemin"?: number | undefined | undefined;
        "aria-valuenow"?: number | undefined | undefined;
        "aria-valuetext"?: string | undefined | undefined;
        dangerouslySetInnerHTML?: {
            __html: string | TrustedHTML;
        } | undefined | undefined;
        onCopy?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCopyCapture?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCut?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCutCapture?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onPaste?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onPasteCapture?: import("react").ClipboardEventHandler<HTMLAnchorElement> | undefined;
        onCompositionEnd?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionStart?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionUpdate?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLAnchorElement> | undefined;
        onFocus?: import("react").FocusEventHandler<HTMLAnchorElement> | undefined;
        onFocusCapture?: import("react").FocusEventHandler<HTMLAnchorElement> | undefined;
        onBlur?: import("react").FocusEventHandler<HTMLAnchorElement> | undefined;
        onBlurCapture?: import("react").FocusEventHandler<HTMLAnchorElement> | undefined;
        onChange?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onChangeCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onBeforeInput?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onBeforeInputCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onInput?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onInputCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onReset?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onResetCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onSubmit?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onSubmitCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onInvalid?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onInvalidCapture?: import("react").FormEventHandler<HTMLAnchorElement> | undefined;
        onLoad?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onErrorCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onKeyDown?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyPress?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyUp?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLAnchorElement> | undefined;
        onAbort?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onAbortCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlay?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayThrough?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onDurationChange?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onDurationChangeCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEmptied?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEmptiedCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEncrypted?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEncryptedCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEnded?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onEndedCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedData?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedDataCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedMetadata?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadStart?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onLoadStartCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPause?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPauseCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlay?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlayCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlaying?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onPlayingCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onProgress?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onProgressCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onRateChange?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onRateChangeCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onResize?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onResizeCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeeked?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeekedCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeeking?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSeekingCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onStalled?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onStalledCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSuspend?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSuspendCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onTimeUpdate?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onVolumeChange?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onWaiting?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onWaitingCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onAuxClick?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onAuxClickCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onClickCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onContextMenu?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onContextMenuCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onDoubleClick?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onDoubleClickCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onDrag?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnd?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEndCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnter?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragEnterCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragExit?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragExitCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragLeave?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragLeaveCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragOver?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragOverCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragStart?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDragStartCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDrop?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onDropCapture?: import("react").DragEventHandler<HTMLAnchorElement> | undefined;
        onMouseDown?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseDownCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseLeave?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseMove?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseMoveCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOut?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOutCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOver?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseOverCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseUp?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onMouseUpCapture?: import("react").MouseEventHandler<HTMLAnchorElement> | undefined;
        onSelect?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onSelectCapture?: import("react").ReactEventHandler<HTMLAnchorElement> | undefined;
        onTouchCancel?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchCancelCapture?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchEnd?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchEndCapture?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchMove?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchMoveCapture?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onTouchStartCapture?: import("react").TouchEventHandler<HTMLAnchorElement> | undefined;
        onPointerDown?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerDownCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerMove?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerMoveCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerUp?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerUpCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerCancel?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerCancelCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerEnter?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerLeave?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOver?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOverCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOut?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onPointerOutCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onGotPointerCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onLostPointerCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLAnchorElement> | undefined;
        onScroll?: import("react").UIEventHandler<HTMLAnchorElement> | undefined;
        onScrollCapture?: import("react").UIEventHandler<HTMLAnchorElement> | undefined;
        onWheel?: import("react").WheelEventHandler<HTMLAnchorElement> | undefined;
        onWheelCapture?: import("react").WheelEventHandler<HTMLAnchorElement> | undefined;
        onAnimationStart?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationEnd?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationIteration?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLAnchorElement> | undefined;
        onToggle?: import("react").ToggleEventHandler<HTMLAnchorElement> | undefined;
        onBeforeToggle?: import("react").ToggleEventHandler<HTMLAnchorElement> | undefined;
        onTransitionCancel?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionCancelCapture?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionEnd?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionRun?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionRunCapture?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionStart?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        onTransitionStartCapture?: import("react").TransitionEventHandler<HTMLAnchorElement> | undefined;
        href: [AppPathnames] extends [never] ? string | import("url").UrlObject : keyof AppPathnames extends infer T ? T extends keyof AppPathnames ? T extends `${string}[[...${string}` ? T | ({
            pathname: T;
            params?: import("../shared/StrictParams.js").default<T> | undefined;
        } & Omit<import("url").UrlObject, "pathname">) : T extends `${string}[${string}` ? {
            pathname: T;
            params: import("../shared/StrictParams.js").default<T>;
        } & Omit<import("url").UrlObject, "pathname"> : T | ({
            pathname: T;
        } & Omit<import("url").UrlObject, "pathname">) : never : never;
    }, "ref"> & import("react").RefAttributes<HTMLAnchorElement>>;
    redirect: (args: {
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: import("../shared/utils.js").QueryParams;
        } : keyof AppPathnames extends infer T_1 ? T_1 extends keyof AppPathnames ? T_1 extends `${string}[[...${string}` ? T_1 | ({
            pathname: T_1;
            params?: import("../shared/StrictParams.js").default<T_1> | undefined;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : T_1 extends `${string}[${string}` ? {
            pathname: T_1;
            params: import("../shared/StrictParams.js").default<T_1>;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        } : T_1 | ({
            pathname: T_1;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : never : never;
        locale: import("use-intl").Locale;
        forcePrefix?: boolean;
    }, type?: import("next/navigation.js").RedirectType | undefined) => never;
    permanentRedirect: (args: {
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: import("../shared/utils.js").QueryParams;
        } : keyof AppPathnames extends infer T_1 ? T_1 extends keyof AppPathnames ? T_1 extends `${string}[[...${string}` ? T_1 | ({
            pathname: T_1;
            params?: import("../shared/StrictParams.js").default<T_1> | undefined;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : T_1 extends `${string}[${string}` ? {
            pathname: T_1;
            params: import("../shared/StrictParams.js").default<T_1>;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        } : T_1 | ({
            pathname: T_1;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : never : never;
        locale: import("use-intl").Locale;
        forcePrefix?: boolean;
    }, type?: import("next/navigation.js").RedirectType | undefined) => never;
    getPathname: (args: {
        href: [AppPathnames] extends [never] ? string | {
            pathname: string;
            query?: import("../shared/utils.js").QueryParams;
        } : keyof AppPathnames extends infer T_1 ? T_1 extends keyof AppPathnames ? T_1 extends `${string}[[...${string}` ? T_1 | ({
            pathname: T_1;
            params?: import("../shared/StrictParams.js").default<T_1> | undefined;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : T_1 extends `${string}[${string}` ? {
            pathname: T_1;
            params: import("../shared/StrictParams.js").default<T_1>;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        } : T_1 | ({
            pathname: T_1;
        } & {
            query?: import("../shared/utils.js").QueryParams;
        }) : never : never;
        locale: import("use-intl").Locale;
        forcePrefix?: boolean;
    }) => string;
};
