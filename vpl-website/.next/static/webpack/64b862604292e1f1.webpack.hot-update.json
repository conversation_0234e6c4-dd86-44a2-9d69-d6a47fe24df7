{"c": ["app/layout", "webpack"], "r": [], "m": [null, "(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js", "(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/core.js", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/index.js", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-DPFnvsUO.js", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js", "(app-pages-browser)/./src/app/globals.css", "(app-pages-browser)/./src/components/Header.tsx"]}