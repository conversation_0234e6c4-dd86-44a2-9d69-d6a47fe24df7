/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact/route";
exports.ids = ["app/api/contact/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Wis_VPL_KHD_cs_vpl_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/contact/route.ts */ \"(rsc)/./src/app/api/contact/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Wis/VPL/KHD/cs/vpl-website/src/app/api/contact/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Wis_VPL_KHD_cs_vpl_website_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/contact/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjb250YWN0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjb250YWN0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY29udGFjdCUyRnJvdXRlLnRzJmFwcERpcj0lMkZWb2x1bWVzJTJGV2lzJTJGVlBMJTJGS0hEJTJGY3MlMkZ2cGwtd2Vic2l0ZSUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVm9sdW1lcyUyRldpcyUyRlZQTCUyRktIRCUyRmNzJTJGdnBsLXdlYnNpdGUmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QmaXNHbG9iYWxOb3RGb3VuZEVuYWJsZWQ9ISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNkO0FBQ1M7QUFDTztBQUNLO0FBQ21DO0FBQ2pEO0FBQ087QUFDZjtBQUNzQztBQUN6QjtBQUNNO0FBQ0M7QUFDaEI7QUFDMkI7QUFDN0Y7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYSxPQUFvQyxJQUFJLENBQUU7QUFDdkQsZ0JBQWdCLE1BQXVDO0FBQ3ZEO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGO0FBQ25GO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsS0FBcUIsRUFBRSxFQUUxQixDQUFDO0FBQ047QUFDQTtBQUNBO0FBQ0EsK0JBQStCLE9BQXdDO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLG9KQUFvSjtBQUNoSyw4QkFBOEIsNkZBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiw2RkFBZTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0RUFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsOEJBQThCLDZFQUFjO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw0RUFBZTtBQUMzQyw0QkFBNEIsNkVBQWdCO0FBQzVDLG9CQUFvQix5R0FBa0Isa0NBQWtDLGlIQUFzQjtBQUM5RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLGdGQUFjO0FBQy9FLCtEQUErRCx5Q0FBeUM7QUFDeEc7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsUUFBUSxFQUFFLE1BQU07QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxrQkFBa0I7QUFDbEIsdUNBQXVDLFFBQVEsRUFBRSxRQUFRO0FBQ3pEO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxvQkFBb0I7QUFDbkU7QUFDQSx5QkFBeUIsNkVBQWM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxzRkFBeUI7QUFDakU7QUFDQSxvQ0FBb0MsNEVBQXNCO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0pBQXNKLG9FQUFjO0FBQ3BLLDBJQUEwSSxvRUFBYztBQUN4SjtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsNkVBQWU7QUFDckQ7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBLDhCQUE4Qiw2RUFBWTtBQUMxQztBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLDJGQUFtQjtBQUNqRTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGtFQUFTO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUlBQXFJLDZFQUFlO0FBQ3BKO0FBQ0EsMkdBQTJHLGlIQUFpSDtBQUM1TjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxpQkFBaUIsNkVBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHdGQUEyQjtBQUN2RCxrQkFBa0IsNkVBQWM7QUFDaEMsK0JBQStCLDRFQUFzQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QywwRkFBcUI7QUFDbEU7QUFDQSxrQkFBa0IsNkVBQVk7QUFDOUI7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsNkVBQTZFLGdGQUFjO0FBQzNGLGlDQUFpQyxRQUFRLEVBQUUsUUFBUTtBQUNuRCwwQkFBMEIsdUVBQVE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDJGQUFtQjtBQUNyRDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2RUFBWTtBQUMxQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgeyBnZXRSZXF1ZXN0TWV0YSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JlcXVlc3QtbWV0YVwiO1xuaW1wb3J0IHsgZ2V0VHJhY2VyLCBTcGFuS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS90cmFjZXJcIjtcbmltcG9ydCB7IG5vcm1hbGl6ZUFwcFBhdGggfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FwcC1wYXRoc1wiO1xuaW1wb3J0IHsgTm9kZU5leHRSZXF1ZXN0LCBOb2RlTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYmFzZS1odHRwL25vZGVcIjtcbmltcG9ydCB7IE5leHRSZXF1ZXN0QWRhcHRlciwgc2lnbmFsRnJvbU5vZGVSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9uZXh0LXJlcXVlc3RcIjtcbmltcG9ydCB7IEJhc2VTZXJ2ZXJTcGFuIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3RyYWNlL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgZ2V0UmV2YWxpZGF0ZVJlYXNvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2luc3RydW1lbnRhdGlvbi91dGlsc1wiO1xuaW1wb3J0IHsgc2VuZFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvc2VuZC1yZXNwb25zZVwiO1xuaW1wb3J0IHsgZnJvbU5vZGVPdXRnb2luZ0h0dHBIZWFkZXJzLCB0b05vZGVPdXRnb2luZ0h0dHBIZWFkZXJzIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvd2ViL3V0aWxzXCI7XG5pbXBvcnQgeyBnZXRDYWNoZUNvbnRyb2xIZWFkZXIgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvY2FjaGUtY29udHJvbFwiO1xuaW1wb3J0IHsgSU5GSU5JVEVfQ0FDSEUsIE5FWFRfQ0FDSEVfVEFHU19IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9jb25zdGFudHNcIjtcbmltcG9ydCB7IE5vRmFsbGJhY2tFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9uby1mYWxsYmFjay1lcnJvci5leHRlcm5hbFwiO1xuaW1wb3J0IHsgQ2FjaGVkUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVzcG9uc2UtY2FjaGVcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9zcmMvYXBwL2FwaS9jb250YWN0L3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jb250YWN0L3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY29udGFjdFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvY29udGFjdC9yb3V0ZVwiXG4gICAgfSxcbiAgICBkaXN0RGlyOiBwcm9jZXNzLmVudi5fX05FWFRfUkVMQVRJVkVfRElTVF9ESVIgfHwgJycsXG4gICAgcHJvamVjdERpcjogcHJvY2Vzcy5lbnYuX19ORVhUX1JFTEFUSVZFX1BST0pFQ1RfRElSIHx8ICcnLFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvc3JjL2FwcC9hcGkvY29udGFjdC9yb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYW5kbGVyKHJlcSwgcmVzLCBjdHgpIHtcbiAgICB2YXIgX25leHRDb25maWdfZXhwZXJpbWVudGFsO1xuICAgIGxldCBzcmNQYWdlID0gXCIvYXBpL2NvbnRhY3Qvcm91dGVcIjtcbiAgICAvLyB0dXJib3BhY2sgZG9lc24ndCBub3JtYWxpemUgYC9pbmRleGAgaW4gdGhlIHBhZ2UgbmFtZVxuICAgIC8vIHNvIHdlIG5lZWQgdG8gdG8gcHJvY2VzcyBkeW5hbWljIHJvdXRlcyBwcm9wZXJseVxuICAgIC8vIFRPRE86IGZpeCB0dXJib3BhY2sgcHJvdmlkaW5nIGRpZmZlcmluZyB2YWx1ZSBmcm9tIHdlYnBhY2tcbiAgICBpZiAocHJvY2Vzcy5lbnYuVFVSQk9QQUNLKSB7XG4gICAgICAgIHNyY1BhZ2UgPSBzcmNQYWdlLnJlcGxhY2UoL1xcL2luZGV4JC8sICcnKSB8fCAnLyc7XG4gICAgfSBlbHNlIGlmIChzcmNQYWdlID09PSAnL2luZGV4Jykge1xuICAgICAgICAvLyB3ZSBhbHdheXMgbm9ybWFsaXplIC9pbmRleCBzcGVjaWZpY2FsbHlcbiAgICAgICAgc3JjUGFnZSA9ICcvJztcbiAgICB9XG4gICAgY29uc3QgbXVsdGlab25lRHJhZnRNb2RlID0gcHJvY2Vzcy5lbnYuX19ORVhUX01VTFRJX1pPTkVfRFJBRlRfTU9ERTtcbiAgICBjb25zdCBwcmVwYXJlUmVzdWx0ID0gYXdhaXQgcm91dGVNb2R1bGUucHJlcGFyZShyZXEsIHJlcywge1xuICAgICAgICBzcmNQYWdlLFxuICAgICAgICBtdWx0aVpvbmVEcmFmdE1vZGVcbiAgICB9KTtcbiAgICBpZiAoIXByZXBhcmVSZXN1bHQpIHtcbiAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDA7XG4gICAgICAgIHJlcy5lbmQoJ0JhZCBSZXF1ZXN0Jyk7XG4gICAgICAgIGN0eC53YWl0VW50aWwgPT0gbnVsbCA/IHZvaWQgMCA6IGN0eC53YWl0VW50aWwuY2FsbChjdHgsIFByb21pc2UucmVzb2x2ZSgpKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGNvbnN0IHsgYnVpbGRJZCwgcGFyYW1zLCBuZXh0Q29uZmlnLCBpc0RyYWZ0TW9kZSwgcHJlcmVuZGVyTWFuaWZlc3QsIHJvdXRlclNlcnZlckNvbnRleHQsIGlzT25EZW1hbmRSZXZhbGlkYXRlLCByZXZhbGlkYXRlT25seUdlbmVyYXRlZCwgcmVzb2x2ZWRQYXRobmFtZSB9ID0gcHJlcGFyZVJlc3VsdDtcbiAgICBjb25zdCBub3JtYWxpemVkU3JjUGFnZSA9IG5vcm1hbGl6ZUFwcFBhdGgoc3JjUGFnZSk7XG4gICAgbGV0IGlzSXNyID0gQm9vbGVhbihwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXSB8fCBwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbcmVzb2x2ZWRQYXRobmFtZV0pO1xuICAgIGlmIChpc0lzciAmJiAhaXNEcmFmdE1vZGUpIHtcbiAgICAgICAgY29uc3QgaXNQcmVyZW5kZXJlZCA9IEJvb2xlYW4ocHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW3Jlc29sdmVkUGF0aG5hbWVdKTtcbiAgICAgICAgY29uc3QgcHJlcmVuZGVySW5mbyA9IHByZXJlbmRlck1hbmlmZXN0LmR5bmFtaWNSb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdO1xuICAgICAgICBpZiAocHJlcmVuZGVySW5mbykge1xuICAgICAgICAgICAgaWYgKHByZXJlbmRlckluZm8uZmFsbGJhY2sgPT09IGZhbHNlICYmICFpc1ByZXJlbmRlcmVkKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IE5vRmFsbGJhY2tFcnJvcigpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGxldCBjYWNoZUtleSA9IG51bGw7XG4gICAgaWYgKGlzSXNyICYmICFyb3V0ZU1vZHVsZS5pc0RldiAmJiAhaXNEcmFmdE1vZGUpIHtcbiAgICAgICAgY2FjaGVLZXkgPSByZXNvbHZlZFBhdGhuYW1lO1xuICAgICAgICAvLyBlbnN1cmUgL2luZGV4IGFuZCAvIGlzIG5vcm1hbGl6ZWQgdG8gb25lIGtleVxuICAgICAgICBjYWNoZUtleSA9IGNhY2hlS2V5ID09PSAnL2luZGV4JyA/ICcvJyA6IGNhY2hlS2V5O1xuICAgIH1cbiAgICBjb25zdCBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSA9IC8vIElmIHdlJ3JlIGluIGRldmVsb3BtZW50LCB3ZSBhbHdheXMgc3VwcG9ydCBkeW5hbWljIEhUTUxcbiAgICByb3V0ZU1vZHVsZS5pc0RldiA9PT0gdHJ1ZSB8fCAvLyBJZiB0aGlzIGlzIG5vdCBTU0cgb3IgZG9lcyBub3QgaGF2ZSBzdGF0aWMgcGF0aHMsIHRoZW4gaXQgc3VwcG9ydHNcbiAgICAvLyBkeW5hbWljIEhUTUwuXG4gICAgIWlzSXNyO1xuICAgIC8vIFRoaXMgaXMgYSByZXZhbGlkYXRpb24gcmVxdWVzdCBpZiB0aGUgcmVxdWVzdCBpcyBmb3IgYSBzdGF0aWNcbiAgICAvLyBwYWdlIGFuZCBpdCBpcyBub3QgYmVpbmcgcmVzdW1lZCBmcm9tIGEgcG9zdHBvbmVkIHJlbmRlciBhbmRcbiAgICAvLyBpdCBpcyBub3QgYSBkeW5hbWljIFJTQyByZXF1ZXN0IHRoZW4gaXQgaXMgYSByZXZhbGlkYXRpb25cbiAgICAvLyByZXF1ZXN0LlxuICAgIGNvbnN0IGlzUmV2YWxpZGF0ZSA9IGlzSXNyICYmICFzdXBwb3J0c0R5bmFtaWNSZXNwb25zZTtcbiAgICBjb25zdCBtZXRob2QgPSByZXEubWV0aG9kIHx8ICdHRVQnO1xuICAgIGNvbnN0IHRyYWNlciA9IGdldFRyYWNlcigpO1xuICAgIGNvbnN0IGFjdGl2ZVNwYW4gPSB0cmFjZXIuZ2V0QWN0aXZlU2NvcGVTcGFuKCk7XG4gICAgY29uc3QgY29udGV4dCA9IHtcbiAgICAgICAgcGFyYW1zLFxuICAgICAgICBwcmVyZW5kZXJNYW5pZmVzdCxcbiAgICAgICAgcmVuZGVyT3B0czoge1xuICAgICAgICAgICAgZXhwZXJpbWVudGFsOiB7XG4gICAgICAgICAgICAgICAgZHluYW1pY0lPOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmR5bmFtaWNJTyksXG4gICAgICAgICAgICAgICAgYXV0aEludGVycnVwdHM6IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuYXV0aEludGVycnVwdHMpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UsXG4gICAgICAgICAgICBpbmNyZW1lbnRhbENhY2hlOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbmNyZW1lbnRhbENhY2hlJyksXG4gICAgICAgICAgICBjYWNoZUxpZmVQcm9maWxlczogKF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbCA9IG5leHRDb25maWcuZXhwZXJpbWVudGFsKSA9PSBudWxsID8gdm9pZCAwIDogX25leHRDb25maWdfZXhwZXJpbWVudGFsLmNhY2hlTGlmZSxcbiAgICAgICAgICAgIGlzUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbCxcbiAgICAgICAgICAgIG9uQ2xvc2U6IChjYik9PntcbiAgICAgICAgICAgICAgICByZXMub24oJ2Nsb3NlJywgY2IpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uQWZ0ZXJUYXNrRXJyb3I6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIG9uSW5zdHJ1bWVudGF0aW9uUmVxdWVzdEVycm9yOiAoZXJyb3IsIF9yZXF1ZXN0LCBlcnJvckNvbnRleHQpPT5yb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVycm9yLCBlcnJvckNvbnRleHQsIHJvdXRlclNlcnZlckNvbnRleHQpXG4gICAgICAgIH0sXG4gICAgICAgIHNoYXJlZENvbnRleHQ6IHtcbiAgICAgICAgICAgIGJ1aWxkSWRcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3Qgbm9kZU5leHRSZXEgPSBuZXcgTm9kZU5leHRSZXF1ZXN0KHJlcSk7XG4gICAgY29uc3Qgbm9kZU5leHRSZXMgPSBuZXcgTm9kZU5leHRSZXNwb25zZShyZXMpO1xuICAgIGNvbnN0IG5leHRSZXEgPSBOZXh0UmVxdWVzdEFkYXB0ZXIuZnJvbU5vZGVOZXh0UmVxdWVzdChub2RlTmV4dFJlcSwgc2lnbmFsRnJvbU5vZGVSZXNwb25zZShyZXMpKTtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBpbnZva2VSb3V0ZU1vZHVsZSA9IGFzeW5jIChzcGFuKT0+e1xuICAgICAgICAgICAgcmV0dXJuIHJvdXRlTW9kdWxlLmhhbmRsZShuZXh0UmVxLCBjb250ZXh0KS5maW5hbGx5KCgpPT57XG4gICAgICAgICAgICAgICAgaWYgKCFzcGFuKSByZXR1cm47XG4gICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgJ2h0dHAuc3RhdHVzX2NvZGUnOiByZXMuc3RhdHVzQ29kZSxcbiAgICAgICAgICAgICAgICAgICAgJ25leHQucnNjJzogZmFsc2VcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCByb290U3BhbkF0dHJpYnV0ZXMgPSB0cmFjZXIuZ2V0Um9vdFNwYW5BdHRyaWJ1dGVzKCk7XG4gICAgICAgICAgICAgICAgLy8gV2Ugd2VyZSB1bmFibGUgdG8gZ2V0IGF0dHJpYnV0ZXMsIHByb2JhYmx5IE9URUwgaXMgbm90IGVuYWJsZWRcbiAgICAgICAgICAgICAgICBpZiAoIXJvb3RTcGFuQXR0cmlidXRlcykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpICE9PSBCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgVW5leHBlY3RlZCByb290IHNwYW4gdHlwZSAnJHtyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpfScuIFBsZWFzZSByZXBvcnQgdGhpcyBOZXh0LmpzIGlzc3VlIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qc2ApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJvdXRlID0gcm9vdFNwYW5BdHRyaWJ1dGVzLmdldCgnbmV4dC5yb3V0ZScpO1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lID0gYCR7bWV0aG9kfSAke3JvdXRlfWA7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlcyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAnbmV4dC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2h0dHAucm91dGUnOiByb3V0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnNwYW5fbmFtZSc6IG5hbWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShuYW1lKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnVwZGF0ZU5hbWUoYCR7bWV0aG9kfSAke3JlcS51cmx9YCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGhhbmRsZVJlc3BvbnNlID0gYXN5bmMgKGN1cnJlbnRTcGFuKT0+e1xuICAgICAgICAgICAgdmFyIF9jYWNoZUVudHJ5X3ZhbHVlO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2VHZW5lcmF0b3IgPSBhc3luYyAoeyBwcmV2aW91c0NhY2hlRW50cnkgfSk9PntcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWdldFJlcXVlc3RNZXRhKHJlcSwgJ21pbmltYWxNb2RlJykgJiYgaXNPbkRlbWFuZFJldmFsaWRhdGUgJiYgcmV2YWxpZGF0ZU9ubHlHZW5lcmF0ZWQgJiYgIXByZXZpb3VzQ2FjaGVFbnRyeSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBvbi1kZW1hbmQgcmV2YWxpZGF0ZSBhbHdheXMgc2V0cyB0aGlzIGhlYWRlclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcigneC1uZXh0anMtY2FjaGUnLCAnUkVWQUxJREFURUQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5lbmQoJ1RoaXMgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW52b2tlUm91dGVNb2R1bGUoY3VycmVudFNwYW4pO1xuICAgICAgICAgICAgICAgICAgICByZXEuZmV0Y2hNZXRyaWNzID0gY29udGV4dC5yZW5kZXJPcHRzLmZldGNoTWV0cmljcztcbiAgICAgICAgICAgICAgICAgICAgbGV0IHBlbmRpbmdXYWl0VW50aWwgPSBjb250ZXh0LnJlbmRlck9wdHMucGVuZGluZ1dhaXRVbnRpbDtcbiAgICAgICAgICAgICAgICAgICAgLy8gQXR0ZW1wdCB1c2luZyBwcm92aWRlZCB3YWl0VW50aWwgaWYgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgIC8vIGlmIGl0J3Mgbm90IHdlIGZhbGxiYWNrIHRvIHNlbmRSZXNwb25zZSdzIGhhbmRsaW5nXG4gICAgICAgICAgICAgICAgICAgIGlmIChwZW5kaW5nV2FpdFVudGlsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY3R4LndhaXRVbnRpbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN0eC53YWl0VW50aWwocGVuZGluZ1dhaXRVbnRpbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGVuZGluZ1dhaXRVbnRpbCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjYWNoZVRhZ3MgPSBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkVGFncztcbiAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIHJlcXVlc3QgaXMgZm9yIGEgc3RhdGljIHJlc3BvbnNlLCB3ZSBjYW4gY2FjaGUgaXQgc28gbG9uZ1xuICAgICAgICAgICAgICAgICAgICAvLyBhcyBpdCdzIG5vdCBlZGdlLlxuICAgICAgICAgICAgICAgICAgICBpZiAoaXNJc3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCByZXNwb25zZS5ibG9iKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBDb3B5IHRoZSBoZWFkZXJzIGZyb20gdGhlIHJlc3BvbnNlLlxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IHRvTm9kZU91dGdvaW5nSHR0cEhlYWRlcnMocmVzcG9uc2UuaGVhZGVycyk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY2FjaGVUYWdzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyc1tORVhUX0NBQ0hFX1RBR1NfSEVBREVSXSA9IGNhY2hlVGFncztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaGVhZGVyc1snY29udGVudC10eXBlJ10gJiYgYmxvYi50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyc1snY29udGVudC10eXBlJ10gPSBibG9iLnR5cGU7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXZhbGlkYXRlID0gdHlwZW9mIGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRSZXZhbGlkYXRlID09PSAndW5kZWZpbmVkJyB8fCBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkUmV2YWxpZGF0ZSA+PSBJTkZJTklURV9DQUNIRSA/IGZhbHNlIDogY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZFJldmFsaWRhdGU7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleHBpcmUgPSB0eXBlb2YgY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZEV4cGlyZSA9PT0gJ3VuZGVmaW5lZCcgfHwgY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZEV4cGlyZSA+PSBJTkZJTklURV9DQUNIRSA/IHVuZGVmaW5lZCA6IGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRFeHBpcmU7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBDcmVhdGUgdGhlIGNhY2hlIGVudHJ5IGZvciB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjYWNoZUVudHJ5ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtpbmQ6IENhY2hlZFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib2R5OiBCdWZmZXIuZnJvbShhd2FpdCBibG9iLmFycmF5QnVmZmVyKCkpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjYWNoZUVudHJ5O1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gc2VuZCByZXNwb25zZSB3aXRob3V0IGNhY2hpbmcgaWYgbm90IElTUlxuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgc2VuZFJlc3BvbnNlKG5vZGVOZXh0UmVxLCBub2RlTmV4dFJlcywgcmVzcG9uc2UsIGNvbnRleHQucmVuZGVyT3B0cy5wZW5kaW5nV2FpdFVudGlsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGlmIHRoaXMgaXMgYSBiYWNrZ3JvdW5kIHJldmFsaWRhdGUgd2UgbmVlZCB0byByZXBvcnRcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhlIHJlcXVlc3QgZXJyb3IgaGVyZSBhcyBpdCB3b24ndCBiZSBidWJibGVkXG4gICAgICAgICAgICAgICAgICAgIGlmIChwcmV2aW91c0NhY2hlRW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZpb3VzQ2FjaGVFbnRyeS5pc1N0YWxlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCByb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVyciwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlcktpbmQ6ICdBcHAgUm91dGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZVBhdGg6IHNyY1BhZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVUeXBlOiAncm91dGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGVSZWFzb246IGdldFJldmFsaWRhdGVSZWFzb24oe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sIHJvdXRlclNlcnZlckNvbnRleHQpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgY2FjaGVFbnRyeSA9IGF3YWl0IHJvdXRlTW9kdWxlLmhhbmRsZVJlc3BvbnNlKHtcbiAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgbmV4dENvbmZpZyxcbiAgICAgICAgICAgICAgICBjYWNoZUtleSxcbiAgICAgICAgICAgICAgICByb3V0ZUtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgICAgICAgICAgaXNGYWxsYmFjazogZmFsc2UsXG4gICAgICAgICAgICAgICAgcHJlcmVuZGVyTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgaXNSb3V0ZVBQUkVuYWJsZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgIHJldmFsaWRhdGVPbmx5R2VuZXJhdGVkLFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlR2VuZXJhdG9yLFxuICAgICAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAvLyB3ZSBkb24ndCBjcmVhdGUgYSBjYWNoZUVudHJ5IGZvciBJU1JcbiAgICAgICAgICAgIGlmICghaXNJc3IpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgoY2FjaGVFbnRyeSA9PSBudWxsID8gdm9pZCAwIDogKF9jYWNoZUVudHJ5X3ZhbHVlID0gY2FjaGVFbnRyeS52YWx1ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9jYWNoZUVudHJ5X3ZhbHVlLmtpbmQpICE9PSBDYWNoZWRSb3V0ZUtpbmQuQVBQX1JPVVRFKSB7XG4gICAgICAgICAgICAgICAgdmFyIF9jYWNoZUVudHJ5X3ZhbHVlMTtcbiAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhcmlhbnQ6IGFwcC1yb3V0ZSByZWNlaXZlZCBpbnZhbGlkIGNhY2hlIGVudHJ5ICR7Y2FjaGVFbnRyeSA9PSBudWxsID8gdm9pZCAwIDogKF9jYWNoZUVudHJ5X3ZhbHVlMSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZTEua2luZH1gKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU3MDFcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFnZXRSZXF1ZXN0TWV0YShyZXEsICdtaW5pbWFsTW9kZScpKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcigneC1uZXh0anMtY2FjaGUnLCBpc09uRGVtYW5kUmV2YWxpZGF0ZSA/ICdSRVZBTElEQVRFRCcgOiBjYWNoZUVudHJ5LmlzTWlzcyA/ICdNSVNTJyA6IGNhY2hlRW50cnkuaXNTdGFsZSA/ICdTVEFMRScgOiAnSElUJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBEcmFmdCBtb2RlIHNob3VsZCBuZXZlciBiZSBjYWNoZWRcbiAgICAgICAgICAgIGlmIChpc0RyYWZ0TW9kZSkge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnLCAncHJpdmF0ZSwgbm8tY2FjaGUsIG5vLXN0b3JlLCBtYXgtYWdlPTAsIG11c3QtcmV2YWxpZGF0ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IGZyb21Ob2RlT3V0Z29pbmdIdHRwSGVhZGVycyhjYWNoZUVudHJ5LnZhbHVlLmhlYWRlcnMpO1xuICAgICAgICAgICAgaWYgKCEoZ2V0UmVxdWVzdE1ldGEocmVxLCAnbWluaW1hbE1vZGUnKSAmJiBpc0lzcikpIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzLmRlbGV0ZShORVhUX0NBQ0hFX1RBR1NfSEVBREVSKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIGNhY2hlIGNvbnRyb2wgaXMgYWxyZWFkeSBzZXQgb24gdGhlIHJlc3BvbnNlIHdlIGRvbid0XG4gICAgICAgICAgICAvLyBvdmVycmlkZSBpdCB0byBhbGxvdyB1c2VycyB0byBjdXN0b21pemUgaXQgdmlhIG5leHQuY29uZmlnXG4gICAgICAgICAgICBpZiAoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wgJiYgIXJlcy5nZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnKSAmJiAhaGVhZGVycy5nZXQoJ0NhY2hlLUNvbnRyb2wnKSkge1xuICAgICAgICAgICAgICAgIGhlYWRlcnMuc2V0KCdDYWNoZS1Db250cm9sJywgZ2V0Q2FjaGVDb250cm9sSGVhZGVyKGNhY2hlRW50cnkuY2FjaGVDb250cm9sKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhd2FpdCBzZW5kUmVzcG9uc2Uobm9kZU5leHRSZXEsIG5vZGVOZXh0UmVzLCBuZXcgUmVzcG9uc2UoY2FjaGVFbnRyeS52YWx1ZS5ib2R5LCB7XG4gICAgICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6IGNhY2hlRW50cnkudmFsdWUuc3RhdHVzIHx8IDIwMFxuICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH07XG4gICAgICAgIC8vIFRPRE86IGFjdGl2ZVNwYW4gY29kZSBwYXRoIGlzIGZvciB3aGVuIHdyYXBwZWQgYnlcbiAgICAgICAgLy8gbmV4dC1zZXJ2ZXIgY2FuIGJlIHJlbW92ZWQgd2hlbiB0aGlzIGlzIG5vIGxvbmdlciB1c2VkXG4gICAgICAgIGlmIChhY3RpdmVTcGFuKSB7XG4gICAgICAgICAgICBhd2FpdCBoYW5kbGVSZXNwb25zZShhY3RpdmVTcGFuKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGF3YWl0IHRyYWNlci53aXRoUHJvcGFnYXRlZENvbnRleHQocmVxLmhlYWRlcnMsICgpPT50cmFjZXIudHJhY2UoQmFzZVNlcnZlclNwYW4uaGFuZGxlUmVxdWVzdCwge1xuICAgICAgICAgICAgICAgICAgICBzcGFuTmFtZTogYCR7bWV0aG9kfSAke3JlcS51cmx9YCxcbiAgICAgICAgICAgICAgICAgICAga2luZDogU3BhbktpbmQuU0VSVkVSLFxuICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5tZXRob2QnOiBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC50YXJnZXQnOiByZXEudXJsXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCBoYW5kbGVSZXNwb25zZSkpO1xuICAgICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIC8vIGlmIHdlIGFyZW4ndCB3cmFwcGVkIGJ5IGJhc2Utc2VydmVyIGhhbmRsZSBoZXJlXG4gICAgICAgIGlmICghYWN0aXZlU3Bhbikge1xuICAgICAgICAgICAgYXdhaXQgcm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnIsIHtcbiAgICAgICAgICAgICAgICByb3V0ZXJLaW5kOiAnQXBwIFJvdXRlcicsXG4gICAgICAgICAgICAgICAgcm91dGVQYXRoOiBub3JtYWxpemVkU3JjUGFnZSxcbiAgICAgICAgICAgICAgICByb3V0ZVR5cGU6ICdyb3V0ZScsXG4gICAgICAgICAgICAgICAgcmV2YWxpZGF0ZVJlYXNvbjogZ2V0UmV2YWxpZGF0ZVJlYXNvbih7XG4gICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgaXNPbkRlbWFuZFJldmFsaWRhdGVcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gcmV0aHJvdyBzbyB0aGF0IHdlIGNhbiBoYW5kbGUgc2VydmluZyBlcnJvciBwYWdlXG4gICAgICAgIC8vIElmIHRoaXMgaXMgZHVyaW5nIHN0YXRpYyBnZW5lcmF0aW9uLCB0aHJvdyB0aGUgZXJyb3IgYWdhaW4uXG4gICAgICAgIGlmIChpc0lzcikgdGhyb3cgZXJyO1xuICAgICAgICAvLyBPdGhlcndpc2UsIHNlbmQgYSA1MDAgcmVzcG9uc2UuXG4gICAgICAgIGF3YWl0IHNlbmRSZXNwb25zZShub2RlTmV4dFJlcSwgbm9kZU5leHRSZXMsIG5ldyBSZXNwb25zZShudWxsLCB7XG4gICAgICAgICAgICBzdGF0dXM6IDUwMFxuICAgICAgICB9KSk7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/notifications/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/admin/notifications/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   broadcastNotification: () => (/* binding */ broadcastNotification),\n/* harmony export */   createNotification: () => (/* binding */ createNotification)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n// Simple in-memory store for active connections\nconst connections = new Set();\n// Function to broadcast notification to all connected clients\nfunction broadcastNotification(notification) {\n    const message = `data: ${JSON.stringify(notification)}\\n\\n`;\n    connections.forEach((controller)=>{\n        try {\n            controller.enqueue(new TextEncoder().encode(message));\n        } catch (error) {\n            // Remove dead connections\n            connections.delete(controller);\n        }\n    });\n}\nasync function GET(request) {\n    // Check authentication\n    const auth = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.requireAuth)(request);\n    if (!auth.authorized) {\n        return new Response('Unauthorized', {\n            status: 401\n        });\n    }\n    // Create a readable stream for Server-Sent Events\n    const stream = new ReadableStream({\n        start (controller) {\n            // Add this connection to our set\n            connections.add(controller);\n            // Send initial connection message\n            const welcomeMessage = {\n                id: Date.now().toString(),\n                type: 'system',\n                title: 'Connected',\n                message: 'Real-time notifications connected',\n                timestamp: new Date().toISOString()\n            };\n            controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(welcomeMessage)}\\n\\n`));\n            // Send keep-alive ping every 30 seconds\n            const keepAlive = setInterval(()=>{\n                try {\n                    controller.enqueue(new TextEncoder().encode(': keep-alive\\n\\n'));\n                } catch (error) {\n                    clearInterval(keepAlive);\n                    connections.delete(controller);\n                }\n            }, 30000);\n            // Clean up when connection closes\n            return ()=>{\n                clearInterval(keepAlive);\n                connections.delete(controller);\n            };\n        },\n        cancel () {\n        // Connection was closed by client\n        }\n    });\n    return new Response(stream, {\n        headers: {\n            'Content-Type': 'text/event-stream',\n            'Cache-Control': 'no-cache',\n            'Connection': 'keep-alive',\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Headers': 'Cache-Control'\n        }\n    });\n}\n// Helper function to create notifications\nfunction createNotification(type, title, message, data) {\n    return {\n        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n        type,\n        title,\n        message,\n        timestamp: new Date().toISOString(),\n        data\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hZG1pbi9ub3RpZmljYXRpb25zL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDeUM7QUFFekMsZ0RBQWdEO0FBQ2hELE1BQU1DLGNBQWMsSUFBSUM7QUFZeEIsOERBQThEO0FBQ3ZELFNBQVNDLHNCQUFzQkMsWUFBMEI7SUFDOUQsTUFBTUMsVUFBVSxDQUFDLE1BQU0sRUFBRUMsS0FBS0MsU0FBUyxDQUFDSCxjQUFjLElBQUksQ0FBQztJQUUzREgsWUFBWU8sT0FBTyxDQUFDLENBQUNDO1FBQ25CLElBQUk7WUFDRkEsV0FBV0MsT0FBTyxDQUFDLElBQUlDLGNBQWNDLE1BQU0sQ0FBQ1A7UUFDOUMsRUFBRSxPQUFPUSxPQUFPO1lBQ2QsMEJBQTBCO1lBQzFCWixZQUFZYSxNQUFNLENBQUNMO1FBQ3JCO0lBQ0Y7QUFDRjtBQUVPLGVBQWVNLElBQUlDLE9BQW9CO0lBQzVDLHVCQUF1QjtJQUN2QixNQUFNQyxPQUFPLE1BQU1qQixzREFBV0EsQ0FBQ2dCO0lBQy9CLElBQUksQ0FBQ0MsS0FBS0MsVUFBVSxFQUFFO1FBQ3BCLE9BQU8sSUFBSUMsU0FBUyxnQkFBZ0I7WUFBRUMsUUFBUTtRQUFJO0lBQ3BEO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU1DLFNBQVMsSUFBSUMsZUFBZTtRQUNoQ0MsT0FBTWQsVUFBVTtZQUNkLGlDQUFpQztZQUNqQ1IsWUFBWXVCLEdBQUcsQ0FBQ2Y7WUFFaEIsa0NBQWtDO1lBQ2xDLE1BQU1nQixpQkFBaUI7Z0JBQ3JCQyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Z0JBQ3ZCQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQMUIsU0FBUztnQkFDVDJCLFdBQVcsSUFBSUwsT0FBT00sV0FBVztZQUNuQztZQUVBeEIsV0FBV0MsT0FBTyxDQUNoQixJQUFJQyxjQUFjQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEVBQUVOLEtBQUtDLFNBQVMsQ0FBQ2tCLGdCQUFnQixJQUFJLENBQUM7WUFHeEUsd0NBQXdDO1lBQ3hDLE1BQU1TLFlBQVlDLFlBQVk7Z0JBQzVCLElBQUk7b0JBQ0YxQixXQUFXQyxPQUFPLENBQUMsSUFBSUMsY0FBY0MsTUFBTSxDQUFDO2dCQUM5QyxFQUFFLE9BQU9DLE9BQU87b0JBQ2R1QixjQUFjRjtvQkFDZGpDLFlBQVlhLE1BQU0sQ0FBQ0w7Z0JBQ3JCO1lBQ0YsR0FBRztZQUVILGtDQUFrQztZQUNsQyxPQUFPO2dCQUNMMkIsY0FBY0Y7Z0JBQ2RqQyxZQUFZYSxNQUFNLENBQUNMO1lBQ3JCO1FBQ0Y7UUFDQTRCO1FBQ0Usa0NBQWtDO1FBQ3BDO0lBQ0Y7SUFFQSxPQUFPLElBQUlsQixTQUFTRSxRQUFRO1FBQzFCaUIsU0FBUztZQUNQLGdCQUFnQjtZQUNoQixpQkFBaUI7WUFDakIsY0FBYztZQUNkLCtCQUErQjtZQUMvQixnQ0FBZ0M7UUFDbEM7SUFDRjtBQUNGO0FBRUEsMENBQTBDO0FBQ25DLFNBQVNDLG1CQUNkVCxJQUEwQixFQUMxQkMsS0FBYSxFQUNiMUIsT0FBZSxFQUNmbUMsSUFBVTtJQUVWLE9BQU87UUFDTGQsSUFBSUMsS0FBS0MsR0FBRyxHQUFHQyxRQUFRLEtBQUtZLEtBQUtDLE1BQU0sR0FBR2IsUUFBUSxDQUFDLElBQUljLE1BQU0sQ0FBQyxHQUFHO1FBQ2pFYjtRQUNBQztRQUNBMUI7UUFDQTJCLFdBQVcsSUFBSUwsT0FBT00sV0FBVztRQUNqQ087SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9zcmMvYXBwL2FwaS9hZG1pbi9ub3RpZmljYXRpb25zL3JvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0IH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgcmVxdWlyZUF1dGggfSBmcm9tICdAL2xpYi9hdXRoJztcblxuLy8gU2ltcGxlIGluLW1lbW9yeSBzdG9yZSBmb3IgYWN0aXZlIGNvbm5lY3Rpb25zXG5jb25zdCBjb25uZWN0aW9ucyA9IG5ldyBTZXQ8UmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcj4oKTtcblxuLy8gTm90aWZpY2F0aW9uIHR5cGVzXG5pbnRlcmZhY2UgTm90aWZpY2F0aW9uIHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogJ25ld19jb250YWN0JyB8ICdzdGF0dXNfdXBkYXRlJyB8ICdzeXN0ZW0nO1xuICB0aXRsZTogc3RyaW5nO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBkYXRhPzogYW55O1xufVxuXG4vLyBGdW5jdGlvbiB0byBicm9hZGNhc3Qgbm90aWZpY2F0aW9uIHRvIGFsbCBjb25uZWN0ZWQgY2xpZW50c1xuZXhwb3J0IGZ1bmN0aW9uIGJyb2FkY2FzdE5vdGlmaWNhdGlvbihub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbikge1xuICBjb25zdCBtZXNzYWdlID0gYGRhdGE6ICR7SlNPTi5zdHJpbmdpZnkobm90aWZpY2F0aW9uKX1cXG5cXG5gO1xuICBcbiAgY29ubmVjdGlvbnMuZm9yRWFjaCgoY29udHJvbGxlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb250cm9sbGVyLmVucXVldWUobmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKG1lc3NhZ2UpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gUmVtb3ZlIGRlYWQgY29ubmVjdGlvbnNcbiAgICAgIGNvbm5lY3Rpb25zLmRlbGV0ZShjb250cm9sbGVyKTtcbiAgICB9XG4gIH0pO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uXG4gIGNvbnN0IGF1dGggPSBhd2FpdCByZXF1aXJlQXV0aChyZXF1ZXN0KTtcbiAgaWYgKCFhdXRoLmF1dGhvcml6ZWQpIHtcbiAgICByZXR1cm4gbmV3IFJlc3BvbnNlKCdVbmF1dGhvcml6ZWQnLCB7IHN0YXR1czogNDAxIH0pO1xuICB9XG5cbiAgLy8gQ3JlYXRlIGEgcmVhZGFibGUgc3RyZWFtIGZvciBTZXJ2ZXItU2VudCBFdmVudHNcbiAgY29uc3Qgc3RyZWFtID0gbmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICBzdGFydChjb250cm9sbGVyKSB7XG4gICAgICAvLyBBZGQgdGhpcyBjb25uZWN0aW9uIHRvIG91ciBzZXRcbiAgICAgIGNvbm5lY3Rpb25zLmFkZChjb250cm9sbGVyKTtcblxuICAgICAgLy8gU2VuZCBpbml0aWFsIGNvbm5lY3Rpb24gbWVzc2FnZVxuICAgICAgY29uc3Qgd2VsY29tZU1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICAgIHR5cGU6ICdzeXN0ZW0nIGFzIGNvbnN0LFxuICAgICAgICB0aXRsZTogJ0Nvbm5lY3RlZCcsXG4gICAgICAgIG1lc3NhZ2U6ICdSZWFsLXRpbWUgbm90aWZpY2F0aW9ucyBjb25uZWN0ZWQnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH07XG5cbiAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShcbiAgICAgICAgbmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKGBkYXRhOiAke0pTT04uc3RyaW5naWZ5KHdlbGNvbWVNZXNzYWdlKX1cXG5cXG5gKVxuICAgICAgKTtcblxuICAgICAgLy8gU2VuZCBrZWVwLWFsaXZlIHBpbmcgZXZlcnkgMzAgc2Vjb25kc1xuICAgICAgY29uc3Qga2VlcEFsaXZlID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShuZXcgVGV4dEVuY29kZXIoKS5lbmNvZGUoJzoga2VlcC1hbGl2ZVxcblxcbicpKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjbGVhckludGVydmFsKGtlZXBBbGl2ZSk7XG4gICAgICAgICAgY29ubmVjdGlvbnMuZGVsZXRlKGNvbnRyb2xsZXIpO1xuICAgICAgICB9XG4gICAgICB9LCAzMDAwMCk7XG5cbiAgICAgIC8vIENsZWFuIHVwIHdoZW4gY29ubmVjdGlvbiBjbG9zZXNcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGNsZWFySW50ZXJ2YWwoa2VlcEFsaXZlKTtcbiAgICAgICAgY29ubmVjdGlvbnMuZGVsZXRlKGNvbnRyb2xsZXIpO1xuICAgICAgfTtcbiAgICB9LFxuICAgIGNhbmNlbCgpIHtcbiAgICAgIC8vIENvbm5lY3Rpb24gd2FzIGNsb3NlZCBieSBjbGllbnRcbiAgICB9LFxuICB9KTtcblxuICByZXR1cm4gbmV3IFJlc3BvbnNlKHN0cmVhbSwge1xuICAgIGhlYWRlcnM6IHtcbiAgICAgICdDb250ZW50LVR5cGUnOiAndGV4dC9ldmVudC1zdHJlYW0nLFxuICAgICAgJ0NhY2hlLUNvbnRyb2wnOiAnbm8tY2FjaGUnLFxuICAgICAgJ0Nvbm5lY3Rpb24nOiAna2VlcC1hbGl2ZScsXG4gICAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctT3JpZ2luJzogJyonLFxuICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LUhlYWRlcnMnOiAnQ2FjaGUtQ29udHJvbCcsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjcmVhdGUgbm90aWZpY2F0aW9uc1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZU5vdGlmaWNhdGlvbihcbiAgdHlwZTogTm90aWZpY2F0aW9uWyd0eXBlJ10sXG4gIHRpdGxlOiBzdHJpbmcsXG4gIG1lc3NhZ2U6IHN0cmluZyxcbiAgZGF0YT86IGFueVxuKTogTm90aWZpY2F0aW9uIHtcbiAgcmV0dXJuIHtcbiAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpLFxuICAgIHR5cGUsXG4gICAgdGl0bGUsXG4gICAgbWVzc2FnZSxcbiAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICBkYXRhLFxuICB9O1xufVxuXG4vLyBFeHBvcnQgZm9yIHVzZSBpbiBvdGhlciBBUEkgcm91dGVzXG5leHBvcnQgeyB0eXBlIE5vdGlmaWNhdGlvbiB9O1xuIl0sIm5hbWVzIjpbInJlcXVpcmVBdXRoIiwiY29ubmVjdGlvbnMiLCJTZXQiLCJicm9hZGNhc3ROb3RpZmljYXRpb24iLCJub3RpZmljYXRpb24iLCJtZXNzYWdlIiwiSlNPTiIsInN0cmluZ2lmeSIsImZvckVhY2giLCJjb250cm9sbGVyIiwiZW5xdWV1ZSIsIlRleHRFbmNvZGVyIiwiZW5jb2RlIiwiZXJyb3IiLCJkZWxldGUiLCJHRVQiLCJyZXF1ZXN0IiwiYXV0aCIsImF1dGhvcml6ZWQiLCJSZXNwb25zZSIsInN0YXR1cyIsInN0cmVhbSIsIlJlYWRhYmxlU3RyZWFtIiwic3RhcnQiLCJhZGQiLCJ3ZWxjb21lTWVzc2FnZSIsImlkIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwidHlwZSIsInRpdGxlIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJrZWVwQWxpdmUiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjYW5jZWwiLCJoZWFkZXJzIiwiY3JlYXRlTm90aWZpY2F0aW9uIiwiZGF0YSIsIk1hdGgiLCJyYW5kb20iLCJzdWJzdHIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/notifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/contact/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/contact/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/email */ \"(rsc)/./src/lib/email.ts\");\n/* harmony import */ var _app_api_admin_notifications_route__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/api/admin/notifications/route */ \"(rsc)/./src/app/api/admin/notifications/route.ts\");\n\n\n\n\n\n// Simple file-based storage for development\nconst getDataFilePath = ()=>{\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data');\n    if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(dataDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().mkdirSync(dataDir, {\n            recursive: true\n        });\n    }\n    return path__WEBPACK_IMPORTED_MODULE_2___default().join(dataDir, 'contacts.json');\n};\nconst loadContacts = ()=>{\n    try {\n        const filePath = getDataFilePath();\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(filePath)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(filePath, 'utf8');\n            return JSON.parse(data);\n        }\n    } catch (error) {\n        console.error('Error loading contacts:', error);\n    }\n    return [];\n};\nconst saveContacts = (contacts)=>{\n    try {\n        const filePath = getDataFilePath();\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(filePath, JSON.stringify(contacts, null, 2));\n    } catch (error) {\n        console.error('Error saving contacts:', error);\n        throw error;\n    }\n};\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'companyName',\n            'contactPerson',\n            'email',\n            'phone',\n            'serviceType',\n            'message'\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Create new contact submission\n        const newContact = {\n            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n            timestamp: new Date().toISOString(),\n            companyName: body.companyName,\n            contactPerson: body.contactPerson,\n            email: body.email,\n            phone: body.phone,\n            wechat: body.wechat || '',\n            qq: body.qq || '',\n            serviceType: body.serviceType,\n            message: body.message,\n            status: 'new'\n        };\n        // Load existing contacts and add new one\n        const contacts = loadContacts();\n        contacts.push(newContact);\n        saveContacts(contacts);\n        // Send email notifications\n        try {\n            // Send admin notification\n            await _lib_email__WEBPACK_IMPORTED_MODULE_3__.emailService.sendAdminNotification(newContact);\n            // Send customer confirmation\n            await _lib_email__WEBPACK_IMPORTED_MODULE_3__.emailService.sendCustomerConfirmation(newContact);\n            console.log('Email notifications sent successfully');\n        } catch (error) {\n            console.error('Error sending email notifications:', error);\n        // Don't fail the request if email fails\n        }\n        // Send real-time notification to admin dashboard\n        try {\n            const notification = (0,_app_api_admin_notifications_route__WEBPACK_IMPORTED_MODULE_4__.createNotification)('new_contact', 'New Business Inquiry', `New inquiry from ${newContact.companyName} - ${newContact.contactPerson}`, {\n                contactId: newContact.id,\n                companyName: newContact.companyName,\n                serviceType: newContact.serviceType\n            });\n            (0,_app_api_admin_notifications_route__WEBPACK_IMPORTED_MODULE_4__.broadcastNotification)(notification);\n            console.log('Real-time notification sent');\n        } catch (error) {\n            console.error('Error sending real-time notification:', error);\n        // Don't fail the request if notification fails\n        }\n        console.log('New contact submission:', newContact);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Contact form submitted successfully',\n            id: newContact.id\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('Error processing contact form:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // This endpoint will be used by the admin dashboard\n        const url = new URL(request.url);\n        const status = url.searchParams.get('status');\n        let contacts = loadContacts();\n        // Filter by status if provided\n        if (status && status !== 'all') {\n            contacts = contacts.filter((contact)=>contact.status === status);\n        }\n        // Sort by timestamp (newest first)\n        contacts.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(contacts);\n    } catch (error) {\n        console.error('Error fetching contacts:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   removeAuthToken: () => (/* binding */ removeAuthToken),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken),\n/* harmony export */   verifyClientAuth: () => (/* binding */ verifyClientAuth),\n/* harmony export */   verifyServerAuth: () => (/* binding */ verifyServerAuth)\n/* harmony export */ });\n// Authentication utilities for client-side and server-side\n// Client-side authentication helpers\nconst getAuthToken = ()=>{\n    if (false) {}\n    return null;\n};\nconst setAuthToken = (token)=>{\n    if (false) {}\n};\nconst removeAuthToken = ()=>{\n    if (false) {}\n};\nconst verifyClientAuth = async ()=>{\n    const token = getAuthToken();\n    if (!token) {\n        return {\n            isAuthenticated: false,\n            user: null,\n            token: null\n        };\n    }\n    try {\n        const response = await fetch('/api/admin/auth', {\n            headers: {\n                'Authorization': `Bearer ${token}`\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                isAuthenticated: true,\n                user: data.user,\n                token\n            };\n        } else {\n            removeAuthToken();\n            return {\n                isAuthenticated: false,\n                user: null,\n                token: null\n            };\n        }\n    } catch (error) {\n        removeAuthToken();\n        return {\n            isAuthenticated: false,\n            user: null,\n            token: null\n        };\n    }\n};\n// Server-side token verification\nconst verifyServerAuth = (token)=>{\n    try {\n        const payload = JSON.parse(Buffer.from(token, 'base64').toString());\n        const isValid = payload.expires > Date.now();\n        return {\n            username: payload.username,\n            valid: isValid\n        };\n    } catch  {\n        return {\n            username: '',\n            valid: false\n        };\n    }\n};\nconst requireAuth = async (request)=>{\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        return {\n            authorized: false\n        };\n    }\n    const token = authHeader.substring(7);\n    const { username, valid } = verifyServerAuth(token);\n    if (!valid) {\n        return {\n            authorized: false\n        };\n    }\n    return {\n        authorized: true,\n        user: {\n            username,\n            role: 'admin'\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailService: () => (/* binding */ EmailService),\n/* harmony export */   emailService: () => (/* binding */ emailService)\n/* harmony export */ });\n// Email service for sending notifications\n// Note: In production, you would install nodemailer: npm install nodemailer @types/nodemailer\n// Default email configuration (should be moved to environment variables)\nconst DEFAULT_EMAIL_CONFIG = {\n    host: process.env.SMTP_HOST || 'smtp.gmail.com',\n    port: parseInt(process.env.SMTP_PORT || '587'),\n    secure: process.env.SMTP_SECURE === 'true',\n    auth: {\n        user: process.env.SMTP_USER || '<EMAIL>',\n        pass: process.env.SMTP_PASS || 'your-app-password'\n    }\n};\n// Mock email service for development\nclass EmailService {\n    constructor(config){\n        this.config = config || DEFAULT_EMAIL_CONFIG;\n    }\n    async sendAdminNotification(submission) {\n        try {\n            // In production, use nodemailer to send actual emails\n            const emailContent = this.generateAdminNotificationEmail(submission);\n            // Mock email sending - log to console for development\n            console.log('📧 Admin Notification Email:');\n            console.log('To:', '<EMAIL>');\n            console.log('Subject:', emailContent.subject);\n            console.log('Body:', emailContent.html);\n            // Simulate email sending delay\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            return true;\n        } catch (error) {\n            console.error('Error sending admin notification:', error);\n            return false;\n        }\n    }\n    async sendCustomerConfirmation(submission) {\n        try {\n            const emailContent = this.generateCustomerConfirmationEmail(submission);\n            // Mock email sending - log to console for development\n            console.log('📧 Customer Confirmation Email:');\n            console.log('To:', submission.email);\n            console.log('Subject:', emailContent.subject);\n            console.log('Body:', emailContent.html);\n            // Simulate email sending delay\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            return true;\n        } catch (error) {\n            console.error('Error sending customer confirmation:', error);\n            return false;\n        }\n    }\n    generateAdminNotificationEmail(submission) {\n        const subject = `New Business Inquiry from ${submission.companyName}`;\n        const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #2563eb;\">New Business Inquiry</h2>\n        \n        <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0; color: #374151;\">Company Information</h3>\n          <p><strong>Company:</strong> ${submission.companyName}</p>\n          <p><strong>Contact Person:</strong> ${submission.contactPerson}</p>\n          <p><strong>Email:</strong> ${submission.email}</p>\n          <p><strong>Phone:</strong> ${submission.phone}</p>\n          ${submission.wechat ? `<p><strong>WeChat:</strong> ${submission.wechat}</p>` : ''}\n          ${submission.qq ? `<p><strong>QQ:</strong> ${submission.qq}</p>` : ''}\n        </div>\n        \n        <div style=\"background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0; color: #374151;\">Service Interest</h3>\n          <p><strong>Service Type:</strong> ${submission.serviceType}</p>\n        </div>\n        \n        <div style=\"background-color: #fefce8; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0; color: #374151;\">Message</h3>\n          <p style=\"white-space: pre-wrap;\">${submission.message}</p>\n        </div>\n        \n        <div style=\"margin-top: 30px; padding: 20px; background-color: #f3f4f6; border-radius: 8px;\">\n          <p style=\"margin: 0; color: #6b7280; font-size: 14px;\">\n            This inquiry was submitted through the VPL website contact form.\n            Please respond within 24 hours to maintain our service standards.\n          </p>\n        </div>\n      </div>\n    `;\n        return {\n            subject,\n            html\n        };\n    }\n    generateCustomerConfirmationEmail(submission) {\n        const subject = 'Thank you for your inquiry - VPL Network Solutions';\n        const html = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"text-align: center; padding: 20px; background-color: #2563eb; color: white; border-radius: 8px 8px 0 0;\">\n          <h1 style=\"margin: 0; font-size: 24px;\">VPL Network Solutions</h1>\n          <p style=\"margin: 10px 0 0 0;\">Professional Network Solutions & VPN Services</p>\n        </div>\n        \n        <div style=\"padding: 30px; background-color: #ffffff; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;\">\n          <h2 style=\"color: #374151; margin-top: 0;\">Thank you for your inquiry!</h2>\n          \n          <p>Dear ${submission.contactPerson},</p>\n          \n          <p>We have received your inquiry regarding <strong>${submission.serviceType}</strong> and appreciate your interest in VPL Network Solutions.</p>\n          \n          <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #374151;\">What happens next?</h3>\n            <ul style=\"color: #6b7280; line-height: 1.6;\">\n              <li>Our technical experts will review your requirements</li>\n              <li>We will contact you within 24 hours via your preferred method</li>\n              <li>We'll schedule a consultation to discuss your specific needs</li>\n              <li>You'll receive a customized solution proposal</li>\n            </ul>\n          </div>\n          \n          <div style=\"background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #374151;\">Contact Information</h3>\n            <p style=\"margin: 5px 0;\"><strong>Email:</strong> <EMAIL></p>\n            <p style=\"margin: 5px 0;\"><strong>Phone:</strong> +86-400-VPL-NET</p>\n            <p style=\"margin: 5px 0;\"><strong>WeChat:</strong> VPL_Support</p>\n            <p style=\"margin: 5px 0;\"><strong>QQ:</strong> 800-888-VPL</p>\n          </div>\n          \n          <p>If you have any urgent questions, please don't hesitate to contact us directly.</p>\n          \n          <p>Best regards,<br>\n          <strong>VPL Network Solutions Team</strong></p>\n        </div>\n        \n        <div style=\"text-align: center; padding: 20px; color: #6b7280; font-size: 12px;\">\n          <p>© 2024 VPL Network Solutions. All rights reserved.</p>\n          <p>This is an automated message. Please do not reply to this email.</p>\n        </div>\n      </div>\n    `;\n        return {\n            subject,\n            html\n        };\n    }\n    // Method to update email configuration\n    updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n    }\n    // Method to test email configuration\n    async testConnection() {\n        try {\n            // In production, test the SMTP connection\n            console.log('Testing email configuration...');\n            console.log('SMTP Host:', this.config.host);\n            console.log('SMTP Port:', this.config.port);\n            console.log('SMTP User:', this.config.auth.user);\n            // Simulate connection test\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            return true;\n        } catch (error) {\n            console.error('Email configuration test failed:', error);\n            return false;\n        }\n    }\n}\n// Export singleton instance\nconst emailService = new EmailService();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FWis%2FVPL%2FKHD%2Fcs%2Fvpl-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();