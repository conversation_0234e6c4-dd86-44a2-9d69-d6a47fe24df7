"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProviderServer)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getConfigNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\");\n/* harmony import */ var _server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/react-server/getFormats.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\");\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\");\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\");\n\n\n\n\n\n\n\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await (0,_server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])() : formats,\n    locale: locale ?? (await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])()),\n    messages: messages === undefined ? await (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await (0,_server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()),\n    timeZone: timeZone ?? (await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()),\n    ...rest\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useConfig.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/useConfig.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useConfig)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/use.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n\n\n\nfunction useHook(hookName, promise) {\n  try {\n    return (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(promise);\n  } catch (error) {\n    if (error instanceof TypeError && error.message.includes(\"Cannot read properties of null (reading 'use')\")) {\n      throw new Error(`\\`${hookName}\\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`, {\n        cause: error\n      });\n    } else {\n      throw error;\n    }\n  }\n}\nfunction useConfig(hookName) {\n  return useHook(hookName, (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci91c2VDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTREO0FBQ3pCOztBQUVuQztBQUNBO0FBQ0EsV0FBVywwREFBRztBQUNkLElBQUk7QUFDSjtBQUNBLDJCQUEyQixTQUFTO0FBQ3BDO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLDZFQUFTO0FBQ3BDOztBQUVnQyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9yZWFjdC1zZXJ2ZXIvdXNlQ29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRDb25maWcgZnJvbSAnLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanMnO1xuaW1wb3J0IHVzZSBmcm9tICcuLi9zaGFyZWQvdXNlLmpzJztcblxuZnVuY3Rpb24gdXNlSG9vayhob29rTmFtZSwgcHJvbWlzZSkge1xuICB0cnkge1xuICAgIHJldHVybiB1c2UocHJvbWlzZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoXCJDYW5ub3QgcmVhZCBwcm9wZXJ0aWVzIG9mIG51bGwgKHJlYWRpbmcgJ3VzZScpXCIpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFxcYCR7aG9va05hbWV9XFxgIGlzIG5vdCBjYWxsYWJsZSB3aXRoaW4gYW4gYXN5bmMgY29tcG9uZW50LiBQbGVhc2UgcmVmZXIgdG8gaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvZW52aXJvbm1lbnRzL3NlcnZlci1jbGllbnQtY29tcG9uZW50cyNhc3luYy1jb21wb25lbnRzYCwge1xuICAgICAgICBjYXVzZTogZXJyb3JcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIHVzZUNvbmZpZyhob29rTmFtZSkge1xuICByZXR1cm4gdXNlSG9vayhob29rTmFtZSwgZ2V0Q29uZmlnKCkpO1xufVxuXG5leHBvcnQgeyB1c2VDb25maWcgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useLocale.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/useLocale.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLocale)\n/* harmony export */ });\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useConfig.js\");\n\n\nfunction useLocale() {\n  const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('useLocale');\n  return config.locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7O0FBRXZDO0FBQ0EsaUJBQWlCLHlEQUFTO0FBQzFCO0FBQ0E7O0FBRWdDIiwic291cmNlcyI6WyIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci91c2VMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHVzZUNvbmZpZyBmcm9tICcuL3VzZUNvbmZpZy5qcyc7XG5cbmZ1bmN0aW9uIHVzZUxvY2FsZSgpIHtcbiAgY29uc3QgY29uZmlnID0gdXNlQ29uZmlnKCd1c2VMb2NhbGUnKTtcbiAgcmV0dXJuIGNvbmZpZy5sb2NhbGU7XG59XG5cbmV4cG9ydCB7IHVzZUxvY2FsZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useTranslations.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/useTranslations.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getServerTranslator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getServerTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useConfig.js\");\n\n\n\nfunction useTranslations(...[namespace]) {\n  const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('useTranslations');\n  return (0,_server_react_server_getServerTranslator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config, namespace);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci91c2VUcmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdGO0FBQ3pDOztBQUV2QztBQUNBLGlCQUFpQix5REFBUztBQUMxQixTQUFTLHVGQUFtQjtBQUM1Qjs7QUFFc0MiLCJzb3VyY2VzIjpbIi9Wb2x1bWVzL1dpcy9WUEwvS0hEL2NzL3ZwbC13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvcmVhY3Qtc2VydmVyL3VzZVRyYW5zbGF0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0U2VydmVyVHJhbnNsYXRvciBmcm9tICcuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFNlcnZlclRyYW5zbGF0b3IuanMnO1xuaW1wb3J0IHVzZUNvbmZpZyBmcm9tICcuL3VzZUNvbmZpZy5qcyc7XG5cbmZ1bmN0aW9uIHVzZVRyYW5zbGF0aW9ucyguLi5bbmFtZXNwYWNlXSkge1xuICBjb25zdCBjb25maWcgPSB1c2VDb25maWcoJ3VzZVRyYW5zbGF0aW9ucycpO1xuICByZXR1cm4gZ2V0U2VydmVyVHJhbnNsYXRvcihjb25maWcsIG5hbWVzcGFjZSk7XG59XG5cbmV4cG9ydCB7IHVzZVRyYW5zbGF0aW9ucyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ getRequestLocale)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\n\nasync function getHeadersImpl() {\n  const promiseOrValue = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n\n  // Compatibility with Next.js <15\n  return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || (await getLocaleFromHeader());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ getCachedRequestLocale),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ setCachedRequestLocale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNENBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwRCIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL1JlcXVlc3RMb2NhbGVDYWNoZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcblxuLy8gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qcy9kaXNjdXNzaW9ucy81ODg2MlxuZnVuY3Rpb24gZ2V0Q2FjaGVJbXBsKCkge1xuICBjb25zdCB2YWx1ZSA9IHtcbiAgICBsb2NhbGU6IHVuZGVmaW5lZFxuICB9O1xuICByZXR1cm4gdmFsdWU7XG59XG5jb25zdCBnZXRDYWNoZSA9IGNhY2hlKGdldENhY2hlSW1wbCk7XG5mdW5jdGlvbiBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlKCkge1xuICByZXR1cm4gZ2V0Q2FjaGUoKS5sb2NhbGU7XG59XG5mdW5jdGlvbiBzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlKGxvY2FsZSkge1xuICBnZXRDYWNoZSgpLmxvY2FsZSA9IGxvY2FsZTtcbn1cblxuZXhwb3J0IHsgZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSwgc2V0Q2FjaGVkUmVxdWVzdExvY2FsZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-DPFnvsUO.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./src/i18n/config.ts\");\n/* harmony import */ var _validateLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\");\n\n\n\n\n\n\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__.getRequestLocale)();\n    }\n  };\n  let result = getConfig(params);\n  if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    (0,_validateLocale_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(receiveRuntimeConfigImpl);\nconst getFormatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.b);\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.d);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(next_intl_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"], localeOverride);\n  return {\n    ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.i)(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBOEI7QUFDd0Q7QUFDcEM7QUFDSTtBQUNOO0FBQ0M7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsNENBQUs7QUFDaEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsbUVBQWdCO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMkRBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDhEQUFjO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qiw0Q0FBSztBQUNsQyxzQkFBc0IsNENBQUssQ0FBQyw0Q0FBcUI7QUFDakQsaUJBQWlCLDRDQUFLLENBQUMsNENBQVk7QUFDbkM7QUFDQSxtREFBbUQsd0RBQWdCO0FBQ25FO0FBQ0EsT0FBTyxnREFBZ0I7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNENBQUs7O0FBRVMiLCJzb3VyY2VzIjpbIi9Wb2x1bWVzL1dpcy9WUEwvS0hEL2NzL3ZwbC13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpbml0aWFsaXplQ29uZmlnLCBfY3JlYXRlSW50bEZvcm1hdHRlcnMsIF9jcmVhdGVDYWNoZSB9IGZyb20gJ3VzZS1pbnRsL2NvcmUnO1xuaW1wb3J0IHsgaXNQcm9taXNlIH0gZnJvbSAnLi4vLi4vc2hhcmVkL3V0aWxzLmpzJztcbmltcG9ydCB7IGdldFJlcXVlc3RMb2NhbGUgfSBmcm9tICcuL1JlcXVlc3RMb2NhbGUuanMnO1xuaW1wb3J0IGdldFJ1bnRpbWVDb25maWcgZnJvbSAnbmV4dC1pbnRsL2NvbmZpZyc7XG5pbXBvcnQgdmFsaWRhdGVMb2NhbGUgZnJvbSAnLi92YWxpZGF0ZUxvY2FsZS5qcyc7XG5cbi8vIFRoaXMgaXMgYXV0b21hdGljYWxseSBpbmhlcml0ZWQgYnkgYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgIGlmXG4vLyB0aGUgY29tcG9uZW50IGlzIHJlbmRlcmVkIGZyb20gYSBTZXJ2ZXIgQ29tcG9uZW50XG5mdW5jdGlvbiBnZXREZWZhdWx0VGltZVpvbmVJbXBsKCkge1xuICByZXR1cm4gSW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lO1xufVxuY29uc3QgZ2V0RGVmYXVsdFRpbWVab25lID0gY2FjaGUoZ2V0RGVmYXVsdFRpbWVab25lSW1wbCk7XG5hc3luYyBmdW5jdGlvbiByZWNlaXZlUnVudGltZUNvbmZpZ0ltcGwoZ2V0Q29uZmlnLCBsb2NhbGVPdmVycmlkZSkge1xuICBpZiAodHlwZW9mIGdldENvbmZpZyAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCBpMThuIHJlcXVlc3QgY29uZmlndXJhdGlvbiBkZXRlY3RlZC5cblxuUGxlYXNlIHZlcmlmeSB0aGF0OlxuMS4gSW4gY2FzZSB5b3UndmUgc3BlY2lmaWVkIGEgY3VzdG9tIGxvY2F0aW9uIGluIHlvdXIgTmV4dC5qcyBjb25maWcsIG1ha2Ugc3VyZSB0aGF0IHRoZSBwYXRoIGlzIGNvcnJlY3QuXG4yLiBZb3UgaGF2ZSBhIGRlZmF1bHQgZXhwb3J0IGluIHlvdXIgaTE4biByZXF1ZXN0IGNvbmZpZ3VyYXRpb24gZmlsZS5cblxuU2VlIGFsc286IGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL3VzYWdlL2NvbmZpZ3VyYXRpb24jaTE4bi1yZXF1ZXN0XG5gKTtcbiAgfVxuICBjb25zdCBwYXJhbXMgPSB7XG4gICAgbG9jYWxlOiBsb2NhbGVPdmVycmlkZSxcbiAgICAvLyBJbiBjYXNlIHRoZSBjb25zdW1lciBkb2Vzbid0IHJlYWQgYHBhcmFtcy5sb2NhbGVgIGFuZCBpbnN0ZWFkIHByb3ZpZGVzIHRoZVxuICAgIC8vIGBsb2NhbGVgIChlaXRoZXIgaW4gYSBzaW5nbGUtbGFuZ3VhZ2Ugd29ya2Zsb3cgb3IgYmVjYXVzZSB0aGUgbG9jYWxlIGlzXG4gICAgLy8gcmVhZCBmcm9tIHRoZSB1c2VyIHNldHRpbmdzKSwgZG9uJ3QgYXR0ZW1wdCB0byByZWFkIHRoZSByZXF1ZXN0IGxvY2FsZS5cbiAgICBnZXQgcmVxdWVzdExvY2FsZSgpIHtcbiAgICAgIHJldHVybiBsb2NhbGVPdmVycmlkZSA/IFByb21pc2UucmVzb2x2ZShsb2NhbGVPdmVycmlkZSkgOiBnZXRSZXF1ZXN0TG9jYWxlKCk7XG4gICAgfVxuICB9O1xuICBsZXQgcmVzdWx0ID0gZ2V0Q29uZmlnKHBhcmFtcyk7XG4gIGlmIChpc1Byb21pc2UocmVzdWx0KSkge1xuICAgIHJlc3VsdCA9IGF3YWl0IHJlc3VsdDtcbiAgfVxuICBpZiAoIXJlc3VsdC5sb2NhbGUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIGxvY2FsZSB3YXMgcmV0dXJuZWQgZnJvbSBgZ2V0UmVxdWVzdENvbmZpZ2AuXFxuXFxuU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL3VzYWdlL2NvbmZpZ3VyYXRpb24jaTE4bi1yZXF1ZXN0Jyk7XG4gIH1cbiAge1xuICAgIHZhbGlkYXRlTG9jYWxlKHJlc3VsdC5sb2NhbGUpO1xuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5jb25zdCByZWNlaXZlUnVudGltZUNvbmZpZyA9IGNhY2hlKHJlY2VpdmVSdW50aW1lQ29uZmlnSW1wbCk7XG5jb25zdCBnZXRGb3JtYXR0ZXJzID0gY2FjaGUoX2NyZWF0ZUludGxGb3JtYXR0ZXJzKTtcbmNvbnN0IGdldENhY2hlID0gY2FjaGUoX2NyZWF0ZUNhY2hlKTtcbmFzeW5jIGZ1bmN0aW9uIGdldENvbmZpZ0ltcGwobG9jYWxlT3ZlcnJpZGUpIHtcbiAgY29uc3QgcnVudGltZUNvbmZpZyA9IGF3YWl0IHJlY2VpdmVSdW50aW1lQ29uZmlnKGdldFJ1bnRpbWVDb25maWcsIGxvY2FsZU92ZXJyaWRlKTtcbiAgcmV0dXJuIHtcbiAgICAuLi5pbml0aWFsaXplQ29uZmlnKHJ1bnRpbWVDb25maWcpLFxuICAgIF9mb3JtYXR0ZXJzOiBnZXRGb3JtYXR0ZXJzKGdldENhY2hlKCkpLFxuICAgIHRpbWVab25lOiBydW50aW1lQ29uZmlnLnRpbWVab25lIHx8IGdldERlZmF1bHRUaW1lWm9uZSgpXG4gIH07XG59XG5jb25zdCBnZXRDb25maWcgPSBjYWNoZShnZXRDb25maWdJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Q29uZmlnIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfigNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getConfigNowImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.now;\n}\nconst getConfigNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigNowImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EscUJBQXFCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldENvbmZpZ05vd0ltcGwobG9jYWxlKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gY29uZmlnLm5vdztcbn1cbmNvbnN0IGdldENvbmZpZ05vdyA9IGNhY2hlKGdldENvbmZpZ05vd0ltcGwpO1xuXG5leHBvcnQgeyBnZXRDb25maWdOb3cgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getFormatsCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.formats;\n}\nconst getFormats = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatsCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDUzs7QUFFdkM7QUFDQSx1QkFBdUIseURBQVM7QUFDaEM7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBSzs7QUFFUyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldEZvcm1hdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0Rm9ybWF0c0NhY2hlZEltcGwoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmZvcm1hdHM7XG59XG5jb25zdCBnZXRGb3JtYXRzID0gY2FjaGUoZ2V0Rm9ybWF0c0NhY2hlZEltcGwpO1xuXG5leHBvcnQgeyBnZXRGb3JtYXRzIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLocaleCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getLocaleCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.locale;\n}\nconst getLocaleCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getLocaleCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0Esd0JBQXdCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldExvY2FsZUNhY2hlZEltcGwoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmxvY2FsZTtcbn1cbmNvbnN0IGdldExvY2FsZUNhY2hlZCA9IGNhY2hlKGdldExvY2FsZUNhY2hlZEltcGwpO1xuXG5leHBvcnQgeyBnZXRMb2NhbGVDYWNoZWQgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMessages),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ getMessagesFromConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EsMEJBQTBCLDRDQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFeUQiLCJzb3VyY2VzIjpbIi9Wb2x1bWVzL1dpcy9WUEwvS0hEL2NzL3ZwbC13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2VydmVyL3JlYWN0LXNlcnZlci9nZXRNZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5mdW5jdGlvbiBnZXRNZXNzYWdlc0Zyb21Db25maWcoY29uZmlnKSB7XG4gIGlmICghY29uZmlnLm1lc3NhZ2VzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdObyBtZXNzYWdlcyBmb3VuZC4gSGF2ZSB5b3UgY29uZmlndXJlZCB0aGVtIGNvcnJlY3RseT8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbWVzc2FnZXMnKTtcbiAgfVxuICByZXR1cm4gY29uZmlnLm1lc3NhZ2VzO1xufVxuYXN5bmMgZnVuY3Rpb24gZ2V0TWVzc2FnZXNDYWNoZWRJbXBsKGxvY2FsZSkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGdldE1lc3NhZ2VzRnJvbUNvbmZpZyhjb25maWcpO1xufVxuY29uc3QgZ2V0TWVzc2FnZXNDYWNoZWQgPSBjYWNoZShnZXRNZXNzYWdlc0NhY2hlZEltcGwpO1xuYXN5bmMgZnVuY3Rpb24gZ2V0TWVzc2FnZXMob3B0cykge1xuICByZXR1cm4gZ2V0TWVzc2FnZXNDYWNoZWQob3B0cz8ubG9jYWxlKTtcbn1cblxuZXhwb3J0IHsgZ2V0TWVzc2FnZXMgYXMgZGVmYXVsdCwgZ2V0TWVzc2FnZXNGcm9tQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getRequestConfig)\n/* harmony export */ });\n/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFJlcXVlc3RDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTaG91bGQgYmUgY2FsbGVkIGluIGBpMThuL3JlcXVlc3QudHNgIHRvIGNyZWF0ZSB0aGUgY29uZmlndXJhdGlvbiBmb3IgdGhlIGN1cnJlbnQgcmVxdWVzdC5cbiAqL1xuZnVuY3Rpb24gZ2V0UmVxdWVzdENvbmZpZyhjcmVhdGVSZXF1ZXN0Q29uZmlnKSB7XG4gIHJldHVybiBjcmVhdGVSZXF1ZXN0Q29uZmlnO1xufVxuXG5leHBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerTranslator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n\n\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getServerTranslatorImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0U2VydmVyVHJhbnNsYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDbUI7O0FBRWpEO0FBQ0EsU0FBUywrREFBZ0I7QUFDekI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDBCQUEwQiw0Q0FBSzs7QUFFVyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFNlcnZlclRyYW5zbGF0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjcmVhdGVUcmFuc2xhdG9yIH0gZnJvbSAndXNlLWludGwvY29yZSc7XG5cbmZ1bmN0aW9uIGdldFNlcnZlclRyYW5zbGF0b3JJbXBsKGNvbmZpZywgbmFtZXNwYWNlKSB7XG4gIHJldHVybiBjcmVhdGVUcmFuc2xhdG9yKHtcbiAgICAuLi5jb25maWcsXG4gICAgbmFtZXNwYWNlXG4gIH0pO1xufVxudmFyIGdldFNlcnZlclRyYW5zbGF0b3IgPSBjYWNoZShnZXRTZXJ2ZXJUcmFuc2xhdG9ySW1wbCk7XG5cbmV4cG9ydCB7IGdldFNlcnZlclRyYW5zbGF0b3IgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeZone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ1M7O0FBRXZDO0FBQ0EsdUJBQXVCLHlEQUFTO0FBQ2hDO0FBQ0E7QUFDQSwwQkFBMEIsNENBQUs7QUFDL0I7QUFDQTtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFRpbWVab25lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldFRpbWVab25lQ2FjaGVkSW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBjb25maWcudGltZVpvbmU7XG59XG5jb25zdCBnZXRUaW1lWm9uZUNhY2hlZCA9IGNhY2hlKGdldFRpbWVab25lQ2FjaGVkSW1wbCk7XG5hc3luYyBmdW5jdGlvbiBnZXRUaW1lWm9uZShvcHRzKSB7XG4gIHJldHVybiBnZXRUaW1lWm9uZUNhY2hlZChvcHRzPy5sb2NhbGUpO1xufVxuXG5leHBvcnQgeyBnZXRUaW1lWm9uZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ validateLocale)\n/* harmony export */ });\nfunction validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixzREFBc0QsT0FBTztBQUM3RDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zZXJ2ZXIvcmVhY3Qtc2VydmVyL3ZhbGlkYXRlTG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHZhbGlkYXRlTG9jYWxlKGxvY2FsZSkge1xuICB0cnkge1xuICAgIGNvbnN0IGNvbnN0cnVjdGVkID0gbmV3IEludGwuTG9jYWxlKGxvY2FsZSk7XG4gICAgaWYgKCFjb25zdHJ1Y3RlZC5sYW5ndWFnZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdMYW5ndWFnZSBpcyByZXF1aXJlZCcpO1xuICAgIH1cbiAgfSBjYXRjaCB7XG4gICAgY29uc29sZS5lcnJvcihgQW4gaW52YWxpZCBsb2NhbGUgd2FzIHByb3ZpZGVkOiBcIiR7bG9jYWxlfVwiXFxuUGxlYXNlIGVuc3VyZSB5b3UncmUgdXNpbmcgYSB2YWxpZCBVbmljb2RlIGxvY2FsZSBpZGVudGlmaWVyIChlLmcuIFwiZW4tVVNcIikuYCk7XG4gIH1cbn1cblxuZXhwb3J0IHsgdmFsaWRhdGVMb2NhbGUgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Volumes/Wis/VPL/KHD/cs/vpl-website/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Volumes/Wis/VPL/KHD/cs/vpl-website/node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ HEADER_LOCALE_NAME)\n/* harmony export */ });\n// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyIvVm9sdW1lcy9XaXMvVlBML0tIRC9jcy92cGwtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVXNlZCB0byByZWFkIHRoZSBsb2NhbGUgZnJvbSB0aGUgbWlkZGxld2FyZVxuY29uc3QgSEVBREVSX0xPQ0FMRV9OQU1FID0gJ1gtTkVYVC1JTlRMLUxPQ0FMRSc7XG5cbmV4cG9ydCB7IEhFQURFUl9MT0NBTEVfTkFNRSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zaGFyZWQvdXNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIHJlYWN0IGZyb20gJ3JlYWN0JztcblxuLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBPb29mLCBOZXh0LmpzIGRvZXNuJ3QgbWFrZSB0aGlzIGVhc3kuXG4vLyBgdXNlYCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBSZWFjdCAxOSBjYW5hcnksIGJ1dCB3ZSBjYW5cbi8vIHVzZSBpdCBpbiBOZXh0LmpzIGFscmVhZHkgYXMgTmV4dC5qcyBcInZlbmRvcnNcIiBhIGZpeGVkXG4vLyB2ZXJzaW9uIG9mIFJlYWN0LiBIb3dldmVyLCBpZiB3ZSdkIHNpbXBseSBwdXQgYHVzZWAgaW5cbi8vIEVTTSBjb2RlLCB0aGVuIHRoZSBidWlsZCBkb2Vzbid0IHdvcmsgc2luY2UgUmVhY3QgZG9lc1xuLy8gbm90IGV4cG9ydCBgdXNlYCBvZmZpY2lhbGx5LiBUaGVyZWZvcmUsIHdlIGhhdmUgdG8gdXNlXG4vLyBzb21ldGhpbmcgdGhhdCBpcyBub3Qgc3RhdGljYWxseSBhbmFseXphYmxlLiBPbmNlIFJlYWN0XG4vLyAxOSBpcyBvdXQsIHdlIGNhbiByZW1vdmUgdGhpcyBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uLlxudmFyIHVzZSA9IHJlYWN0Wyd1c2UnLnRyaW0oKV07XG5cbmV4cG9ydCB7IHVzZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  const [path, ...hashParts] = pathname.split('#');\n  const hash = hashParts.join('#');\n  let normalizedPath = path;\n  if (normalizedPath !== '/') {\n    const pathnameEndsWithSlash = normalizedPath.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      normalizedPath += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n  }\n  if (hash) {\n    normalizedPath += '#' + hash;\n  }\n  return normalizedPath;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-client/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlError),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlErrorCode),\n/* harmony export */   IntlProvider: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlProvider),\n/* harmony export */   _createCache: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createCache),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createIntlFormatters),\n/* harmony export */   createFormatter: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createFormatter),\n/* harmony export */   createTranslator: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createTranslator),\n/* harmony export */   hasLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.initializeConfig),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useLocale),\n/* harmony export */   useMessages: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useMessages),\n/* harmony export */   useNow: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useNow),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/index.js\");\n\n\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore React attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', use_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations);\nconst useFormatter = callHook('useFormatter', use_intl__WEBPACK_IMPORTED_MODULE_1__.useFormatter);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/server/react-client/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-client/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFormatter: () => (/* binding */ getFormatter),\n/* harmony export */   getLocale: () => (/* binding */ getLocale),\n/* harmony export */   getMessages: () => (/* binding */ getMessages),\n/* harmony export */   getNow: () => (/* binding */ getNow),\n/* harmony export */   getRequestConfig: () => (/* binding */ getRequestConfig),\n/* harmony export */   getTimeZone: () => (/* binding */ getTimeZone),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   setRequestLocale: () => (/* binding */ setRequestLocale)\n/* harmony export */ });\n/**\n * Allows to import `next-intl/server` in non-RSC environments.\n *\n * This is mostly relevant for testing, since e.g. a `generateMetadata`\n * export from a page might use `next-intl/server`, but the test\n * only uses the default export for a page.\n */\n\nfunction notSupported(message) {\n  return () => {\n    throw new Error(`\\`${message}\\` is not supported in Client Components.`);\n  };\n}\nfunction getRequestConfig(\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n...args) {\n  return notSupported('getRequestConfig');\n}\nconst getFormatter = notSupported('getFormatter');\nconst getNow = notSupported('getNow');\nconst getTimeZone = notSupported('getTimeZone');\nconst getMessages = notSupported('getMessages');\nconst getLocale = notSupported('getLocale');\n\n// The type of `getTranslations` is not assigned here because it\n// causes a type error. The types use the `react-server` entry\n// anyway, therefore this is irrelevant.\nconst getTranslations = notSupported('getTranslations');\nconst setRequestLocale = notSupported('setRequestLocale');\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/server/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider({ locale, ...rest }) {\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFDOEM7QUFDTjtBQUV4QyxTQUFTRSx1QkFBdUIsRUFDOUJDLE1BQU0sRUFDTixHQUFHQyxNQUNKO0lBQ0MsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtBQUU2QyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvV2lzL1ZQTC9LSEQvY3MvdnBsLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IEludGxQcm92aWRlciB9IGZyb20gJ3VzZS1pbnRsL3JlYWN0JztcbmltcG9ydCB7IGpzeCB9IGZyb20gJ3JlYWN0L2pzeC1ydW50aW1lJztcblxuZnVuY3Rpb24gTmV4dEludGxDbGllbnRQcm92aWRlcih7XG4gIGxvY2FsZSxcbiAgLi4ucmVzdFxufSkge1xuICBpZiAoIWxvY2FsZSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkNvdWxkbid0IGluZmVyIHRoZSBgbG9jYWxlYCBwcm9wIGluIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCwgcGxlYXNlIHByb3ZpZGUgaXQgZXhwbGljaXRseS5cXG5cXG5TZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNsb2NhbGVcIiApO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovanN4KEludGxQcm92aWRlciwge1xuICAgIGxvY2FsZTogbG9jYWxlLFxuICAgIC4uLnJlc3RcbiAgfSk7XG59XG5cbmV4cG9ydCB7IE5leHRJbnRsQ2xpZW50UHJvdmlkZXIgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbIkludGxQcm92aWRlciIsImpzeCIsIk5leHRJbnRsQ2xpZW50UHJvdmlkZXIiLCJsb2NhbGUiLCJyZXN0IiwiRXJyb3IiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n");

/***/ })

};
;