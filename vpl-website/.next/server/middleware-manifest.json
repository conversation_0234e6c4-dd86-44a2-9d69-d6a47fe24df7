{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W6+s3JkEm+yv8wY85WIJ1Y8IgVULXxNcbnZc817W+uo=", "__NEXT_PREVIEW_MODE_ID": "b7918208e2f907177427f52c0af08709", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ba61ae4a3dec914403de5a5f5da24251aae0882dcd02e15384f2d16082944726", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "40edded4453bc626d151cfeac7bfdd4fb724dcf7f0df6795f5045af29a9f8a9e"}}}, "functions": {}, "sortedMiddleware": ["/"]}