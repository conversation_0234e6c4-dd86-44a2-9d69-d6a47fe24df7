{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rXlo8C3xhrAYLe8X0+6wek3PS3PL5hSHwqRase60XV0=", "__NEXT_PREVIEW_MODE_ID": "3effcca6d1af0136670b673bf2273096", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "97b9c4a354a1f779c128bf210ba9a70747b2ca2a348a55f4c446c346ea890eca", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e32416721c46874836c090a114d3b37bd94fa43fd9819fde5b568308ddb37af"}}}, "functions": {}, "sortedMiddleware": ["/"]}