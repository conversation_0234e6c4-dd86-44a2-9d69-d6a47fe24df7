{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7CEbYdSRgW4YDvcM2Wy+Oc46c1JlqBtgXT4EMpB8NE0=", "__NEXT_PREVIEW_MODE_ID": "b80856cd9db34dabd1ad4c0a69a5efaa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46eb691c2b38eab603be9cb2e674b7abe8ebc5ec961eb1de5bfdd5aed8137569", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "59ff04d4ea43c07fad69ba207528329184f15d174445646a6a53366ce9cbc299"}}}, "functions": {}, "sortedMiddleware": ["/"]}