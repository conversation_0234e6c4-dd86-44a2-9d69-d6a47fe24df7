# VPL Network Solutions - B2B Website

A complete B2B website for VPL brand's foreign trade network lines and VPN services, built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

### Frontend Features
- **Professional B2B Design**: Modern, responsive design optimized for business clients
- **Multi-page Structure**: Homepage, Services, Features, and Contact pages
- **Responsive Layout**: Cross-platform compatibility for mobile, tablet, and desktop
- **Advanced Encryption Showcase**: Detailed information about AES, RSA, TLS, tunnel encryption, and Shadowsocks
- **Service Offerings**: Foreign trade networks, cross-border e-commerce solutions, and enterprise VPN services

### Contact System
- **Comprehensive Contact Form**: Multiple communication options (WeChat, QQ, phone, email, SMS)
- **Alphanumeric Verification**: Secure verification code system for form submissions
- **Email Integration**: Automated email notifications for admins and customers
- **Real-time Notifications**: Live updates for new form submissions

### Admin Dashboard
- **Secure Authentication**: Admin login system with token-based authentication
- **Contact Management**: View, filter, and manage all contact submissions
- **Status Tracking**: Track inquiry status (new, contacted, closed)
- **Email Configuration**: Configurable SMTP settings for email notifications
- **Real-time Updates**: Live notifications for new submissions

## 🛠 Technology Stack

- **Framework**: Next.js 15.4.2 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **Authentication**: Custom JWT-like token system
- **Email**: Mock email service (ready for nodemailer integration)
- **Real-time**: Server-Sent Events (SSE)
- **Data Storage**: File-based JSON storage (development)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Admin Access
- **URL**: [http://localhost:3000/admin/login](http://localhost:3000/admin/login)
- **Username**: `admin`
- **Password**: `vpl2024`

## 📧 Email Configuration

The website includes email integration for notifications. To configure:

1. Access the admin dashboard
2. Navigate to Settings
3. Configure SMTP settings for production use

## 📱 Responsive Design

The website is fully responsive and optimized for:
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

## 🔒 Security Features

- **Form Validation**: Client and server-side validation
- **Verification Codes**: Alphanumeric codes for form submissions
- **Authentication**: Secure admin login system
- **Input Sanitization**: Protection against common attacks

## 📊 Features Overview

### Contact Form Features
- ✅ Company information collection
- ✅ Multiple contact methods (WeChat, QQ, phone, email)
- ✅ Service type selection
- ✅ Alphanumeric verification codes
- ✅ Real-time validation
- ✅ Email notifications

### Admin Dashboard Features
- ✅ Contact submission management
- ✅ Status tracking and updates
- ✅ Real-time notifications
- ✅ Email configuration
- ✅ Responsive design
- ✅ Secure authentication

**VPL Network Solutions** - Professional network solutions for global business.
